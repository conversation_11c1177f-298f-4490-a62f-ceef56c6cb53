<?xml version="1.0"?>
<ruleset
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="vendor/squizlabs/php_codesniffer/phpcs.xsd"
    name="Developartment Coding Standard"
>
    <arg name="basepath" value="."/>
    <arg name="extensions" value="php"/>
    <arg name="parallel" value="80"/>
    <arg name="report-width" value="auto"/>
    <arg name="tab-width" value="4"/>
    <arg name="encoding" value="utf-8"/>
    <arg name="cache" value="var/phpcs.cache"/>
    <arg name="colors"/>
    <arg value="s"/>
    <arg value="p"/>

    <file>bin/</file>
    <file>docker/</file>
    <file>docs/</file>
    <file>migrations/</file>
    <file>public/</file>
    <file>src/</file>
    <file>templates/</file>
    <file>tests/</file>
    <file>tools/</file>
    <exclude-pattern>vendor/</exclude-pattern>
    <exclude-pattern>src/DataFixtures/AppFixtures.php</exclude-pattern>
    <arg name="report-width" value="200"/>
<!--    <arg name="report" value="Build\CodingStyle\Helpers\CodingStyleReport"/>-->
    <rule ref="PSR1.Files.SideEffects"/>
    <rule ref="Generic.Arrays.DisallowLongArraySyntax"/>
    <rule ref="Generic.Classes.DuplicateClassName"/>
    <rule ref="Generic.CodeAnalysis.EmptyStatement">
        <exclude name="Generic.CodeAnalysis.EmptyStatement.DetectedCatch"/><!-- empty catch statements are allowed when commented -->
    </rule>
    <rule ref="Generic.CodeAnalysis.ForLoopShouldBeWhileLoop"/>
    <rule ref="Generic.CodeAnalysis.UnconditionalIfStatement"/>
    <rule ref="Generic.CodeAnalysis.UnnecessaryFinalModifier"/>
    <rule ref="Generic.Files.ByteOrderMark"/>
    <rule ref="Generic.Files.EndFileNewline"/>
    <rule ref="Generic.Files.InlineHTML"/>
    <rule ref="Generic.Files.LineEndings"/>
    <rule ref="Generic.Files.OneClassPerFile"/>
    <rule ref="Generic.Files.OneInterfacePerFile"/>
    <rule ref="Generic.Files.OneTraitPerFile"/>
    <rule ref="Generic.Formatting.SpaceAfterNot">
        <properties>
            <property name="spacing" value="0"/>
        </properties>
    </rule>
    <rule ref="Generic.Formatting.SpaceAfterCast"/>
    <rule ref="Generic.Formatting.MultipleStatementAlignment">
        <properties>
            <property name="maxPadding" value="1"/>
        </properties>
    </rule>
    <rule ref="Generic.Functions.CallTimePassByReference"/>
    <rule ref="Generic.Functions.OpeningFunctionBraceBsdAllman"/>
    <rule ref="Generic.NamingConventions.ConstructorName"/>
    <rule ref="Generic.PHP.BacktickOperator"/>
    <rule ref="Generic.PHP.DiscourageGoto"/>
    <rule ref="Generic.PHP.LowerCaseConstant"/>
    <rule ref="Generic.PHP.LowerCaseKeyword"/>
    <rule ref="Generic.PHP.LowerCaseType"/>
    <rule ref="Generic.PHP.CharacterBeforePHPOpeningTag"/>
    <rule ref="Generic.PHP.DeprecatedFunctions"/>
    <rule ref="Generic.PHP.DisallowAlternativePHPTags"/>
    <rule ref="Generic.PHP.DisallowShortOpenTag"/>
    <rule ref="Generic.PHP.ForbiddenFunctions">
        <properties>
            <property
                name="forbiddenFunctions"
                type="array"
                value="
                    mt_rand=>rand,
                    sizeof=>count,
                    delete=>unset,
                    print=>echo,
                    join=>implode,
                    split=>explode,
                    is_null=>null,
                    create_function=>null,
                    key_exists=>array_key_exists,
                    file_get_contents=>Nette\Utils\FileSystem::read,
                    file_put_contents=>Nette\Utils\FileSystem::write,
                    unlink=>Nette\Utils\FileSystem::delete,
                    mkdir=>Nette\Utils\FileSystem::createDir,
                    floatval=>null,
                    boolval=>null,
                    intval=>null,
                    strval=>null,
                    settype=>null,
                    exit=>null,
                    json_decode=>Nette\Utils\Json::decode,
                    json_encode=>Nette\Utils\Json::encode,
                    GuzzleHttp\json_encode=>Nette\Utils\Json::encode,
                    GuzzleHttp\json_decode=>Nette\Utils\Json::decode
                "
            />
        </properties>
    </rule>
    <rule ref="Generic.Strings.UnnecessaryStringConcat">
        <properties>
            <property name="allowMultiline" value="true"/>
        </properties>
    </rule>
    <rule ref="Generic.VersionControl.GitMergeConflict"/>
    <rule ref="Generic.WhiteSpace.IncrementDecrementSpacing"/>
    <rule ref="Generic.WhiteSpace.ScopeIndent">
        <properties>
            <property name="tabIndent" value="false"/>
        </properties>
    </rule>
    <rule ref="PEAR.Classes.ClassDeclaration"/>
    <rule ref="PEAR.Commenting.InlineComment"/>
    <rule ref="PEAR.Formatting.MultiLineAssignment"/>
    <rule ref="PEAR.WhiteSpace.ObjectOperatorIndent"/>
    <rule ref="PSR1.Classes.ClassDeclaration.MissingNamespace"/>
    <rule ref="PSR1.Methods.CamelCapsMethodName.NotCamelCaps"/>
    <rule ref="PSR2">
        <exclude name="Generic.Files.LineLength"/><!-- can not be suppressed -->
        <exclude name="Generic.WhiteSpace.DisallowTabIndent"/><!-- don't check indentation with spaces -->
        <exclude name="PEAR.Functions.ValidDefaultValue"/><!-- we want to allow null as "default" value -->
        <exclude name="PSR2.Classes.ClassDeclaration"/><!-- we want whitespace around class body and rules for extends and implements, using PEAR instead -->
        <exclude name="PSR2.ControlStructures.ControlStructureSpacing.SpacingAfterOpenBrace"/><!-- we want to put first expression of multiline condition on next line -->
        <exclude name="PSR2.ControlStructures.SwitchDeclaration.caseIndent"/><!-- checked by more generic Generic.WhiteSpace.ScopeIndent.Incorrect -->
        <exclude name="PSR2.ControlStructures.SwitchDeclaration.defaultIndent"/><!-- checked by more generic Generic.WhiteSpace.ScopeIndent.Incorrect -->
        <exclude name="Squiz.Functions.LowercaseFunctionKeywords"/><!-- checked by more generic Generic.PHP.LowerCaseKeyword -->
        <exclude name="Squiz.ControlStructures.LowercaseDeclaration"/><!-- checked by more generic Generic.PHP.LowerCaseKeyword -->
        <exclude name="Squiz.Functions.MultiLineFunctionDeclaration.EmptyLine"/><!-- empty lines are useful for promoted properties -->
        <exclude name="Squiz.Functions.MultiLineFunctionDeclaration.NewlineBeforeOpenBrace"/><!-- clashes with OpeningFunctionBraceBsdAllman -->
        <exclude name="Squiz.WhiteSpace.ControlStructureSpacing.SpacingAfterOpenBrace"/><!-- we want to put first expression of multiline condition on next line -->
        <exclude name="Squiz.WhiteSpace.ControlStructureSpacing.SpacingBeforeClose"/><!-- we want to allow empty line in control structure e.g. in try blocks, where it can improve readability -->
    </rule>
    <rule ref="PSR12.Classes.ClassInstantiation"/>
    <rule ref="PSR12.Functions.NullableTypeDeclaration"/>
    <rule ref="PSR12.Keywords.ShortFormTypeKeywords"/>
    <rule ref="PSR12.Operators.OperatorSpacing"/>
    <rule ref="Squiz.Arrays.ArrayBracketSpacing"/>
<!--    <rule ref="Squiz.NamingConventions.ValidVariableName"/>-->
<!--    <rule ref="Squiz.NamingConventions.ValidVariableName.PrivateNoUnderscore">-->
<!--        <severity>0</severity>-->
<!--    </rule>-->
    <rule ref="Squiz.Arrays.ArrayDeclaration">
        <exclude name="Squiz.Arrays.ArrayDeclaration.CloseBraceNewLine"/><!-- does not handle wrapped content -->
        <exclude name="Squiz.Arrays.ArrayDeclaration.CloseBraceNotAligned"/><!-- expects closing brace at the same level as opening brace -->
        <exclude name="Squiz.Arrays.ArrayDeclaration.DoubleArrowNotAligned"/><!-- we don't want spacing with alignment -->
        <exclude name="Squiz.Arrays.ArrayDeclaration.FirstIndexNoNewline"/><!-- expects multi-value array always written on multiple lines -->
        <exclude name="Squiz.Arrays.ArrayDeclaration.FirstValueNoNewline"/><!-- expects multi-value array always written on multiple lines -->
        <exclude name="Squiz.Arrays.ArrayDeclaration.IndexNoNewline"/><!-- false positives in 3.5.5: https://github.com/squizlabs/PHP_CodeSniffer/issues/2937 -->
        <exclude name="Squiz.Arrays.ArrayDeclaration.KeyNotAligned"/><!-- uses indentation of only single space -->
        <exclude name="Squiz.Arrays.ArrayDeclaration.MultiLineNotAllowed"/><!-- even a single-value array can be written on multiple lines -->
        <exclude name="Squiz.Arrays.ArrayDeclaration.NoCommaAfterLast"/><!-- expects multi-value array always written on multiple lines -->
        <exclude name="Squiz.Arrays.ArrayDeclaration.SingleLineNotAllowed"/><!-- multiple values can be written on a single line -->
        <exclude name="Squiz.Arrays.ArrayDeclaration.ValueNoNewline"/><!-- false positives in 3.5.5: https://github.com/squizlabs/PHP_CodeSniffer/issues/2937 -->
        <exclude name="Squiz.Arrays.ArrayDeclaration.ValueNotAligned"/><!-- we don't want spacing with alignment -->
    </rule>
    <rule ref="Squiz.ControlStructures.InlineIfDeclaration">
        <exclude name="Squiz.ControlStructures.InlineIfDeclaration.ElvisSpacing"/>
        <exclude name="Squiz.ControlStructures.InlineIfDeclaration.NoBrackets"/>
        <exclude name="Squiz.ControlStructures.InlineIfDeclaration.NotSingleLine"/>
    </rule>
    <rule ref="Squiz.Classes.ClassFileName"/>
    <rule ref="Squiz.Classes.SelfMemberReference"/>
    <rule ref="Squiz.Commenting.DocCommentAlignment">
        <exclude name="Squiz.Commenting.DocCommentAlignment.SpaceAfterStar"/><!-- space needed for indented annotations -->
    </rule>
    <rule ref="Squiz.Commenting.EmptyCatchComment"/>
    <rule ref="Squiz.Commenting.FunctionComment">
        <exclude name="Squiz.Commenting.FunctionComment.MissingParamTag"/> <!-- we use params only when typehint is not possible -->
        <exclude name="Squiz.Commenting.FunctionComment.ParamNameNoMatch"/> <!-- we use params only when typehint is not possible -->
        <exclude name="Squiz.Commenting.FunctionComment.InvalidTypeHint"/> <!-- we use params only when typehint is not possible -->
        <exclude name="Squiz.Commenting.FunctionComment.EmptyThrows"/> <!-- comment is not needed for throws phpdoc -->
        <exclude name="Squiz.Commenting.FunctionComment.IncorrectParamVarName"/><!-- requires long boolean and integer parameters -->
        <exclude name="Squiz.Commenting.FunctionComment.IncorrectTypeHint"/><!-- collection syntax such as string[] is not supported -->
        <exclude name="Squiz.Commenting.FunctionComment.InvalidReturn"/><!-- enforces incorrect types -->
        <exclude name="Squiz.Commenting.FunctionComment.InvalidReturnNotVoid"/><!-- is not able to detect return types such as string|null as correct -->
        <exclude name="Squiz.Commenting.FunctionComment.Missing"/><!-- PHPDoc is not required on all methods -->
        <exclude name="Squiz.Commenting.FunctionComment.MissingParamComment"/><!-- comments are not required for @param -->
        <exclude name="Squiz.Commenting.FunctionComment.MissingReturn"/><!-- void type is not used -->
        <exclude name="Squiz.Commenting.FunctionComment.ParamCommentFullStop"/><!-- comments don't have to be sentences -->
        <exclude name="Squiz.Commenting.FunctionComment.ParamCommentNotCapital"/><!-- comments don't have to be sentences -->
        <exclude name="Squiz.Commenting.FunctionComment.ScalarTypeHintMissing"/><!-- problematic when extending non-PHP7 classes -->
        <exclude name="Squiz.Commenting.FunctionComment.SpacingAfterParamName"/><!-- we don't want spacing with alignment -->
        <exclude name="Squiz.Commenting.FunctionComment.SpacingAfterParamType"/><!-- we don't want spacing with alignment -->
        <exclude name="Squiz.Commenting.FunctionComment.ThrowsNoFullStop"/><!-- comments don't have to be sentences -->
        <exclude name="Squiz.Commenting.FunctionComment.ThrowsNotCapital"/><!-- comments don't have to be sentences -->
        <exclude name="Squiz.Commenting.FunctionComment.TypeHintMissing"/><!-- doesn't work with self as typehint -->
        <exclude name="Squiz.Commenting.FunctionComment.InvalidNoReturn"/><!-- doesn't work with exceptions (i.e. NotImplementedException) -->
    </rule>
    <rule ref="SlevomatCodingStandard.TypeHints.PropertyTypeHint">
        <properties>
            <property name="enableNativeTypeHint" value="true"/>
            <property name="traversableTypeHints" type="array" value="
                Generator,
                Traversable,
                Doctrine\Common\Collections\Collection,
                Doctrine\Common\Collections\ArrayCollection
            "/>
            <property name="enableMixedTypeHint" value="true"/>
            <property name="enableUnionTypeHint" value="true"/>
        </properties>
    </rule>
    <rule ref="Squiz.Commenting.FunctionComment.InvalidThrows">
        <message>Exception type missing for @throws annotation</message>
    </rule>
    <rule ref="Squiz.Commenting.FunctionComment.DuplicateReturn">
        <message>Only 1 @return annotation is allowed in a function comment</message>
    </rule>
    <rule ref="Squiz.Commenting.FunctionComment.ExtraParamComment">
        <message>Extra @param annotation</message>
    </rule>
    <rule ref="Squiz.Commenting.FunctionComment.MissingParamTag">
        <message>@param annotation for parameter "%s" missing</message>
    </rule>
    <rule ref="Squiz.Commenting.InlineComment">
        <exclude name="Squiz.Commenting.InlineComment.NotCapital"/> <!-- no need to write full sentence -->
        <exclude name="Squiz.Commenting.InlineComment.InvalidEndChar"/> <!-- no need to write full sentence -->
        <exclude name="Squiz.Commenting.InlineComment.DocBlock"/> <!-- needed for inline phpdoc -->
    </rule>
    <rule ref="Squiz.Functions.GlobalFunction"/>
    <rule ref="Squiz.Functions.FunctionDeclarationArgumentSpacing">
        <properties>
            <property name="equalsSpacing" value="1"/>
        </properties>
    </rule>
    <rule ref="Squiz.Operators.IncrementDecrementUsage">
        <exclude name="Squiz.Operators.IncrementDecrementUsage.NoBrackets"/><!-- afaik there is no need for brackets -->
    </rule>
    <rule ref="Squiz.Operators.ValidLogicalOperators"/>
    <rule ref="Squiz.PHP.CommentedOutCode"/>
    <rule ref="Squiz.PHP.GlobalKeyword"/>
    <rule ref="Squiz.PHP.Heredoc">
        <exclude name="Squiz.PHP.Heredoc.NotAllowed"/> <!-- TODO remove in future -->
    </rule>
    <rule ref="Squiz.PHP.InnerFunctions"/>
    <rule ref="Squiz.PHP.LowercasePHPFunctions"/>
    <rule ref="Squiz.PHP.NonExecutableCode"/>
    <rule ref="Squiz.Scope.StaticThisUsage"/>
    <rule ref="Squiz.Scope.MethodScope"/>
    <rule ref="Squiz.Strings.ConcatenationSpacing">
        <properties>
            <property name="ignoreNewlines" value="true"/>
            <property name="spacing" value="1"/>
        </properties>
    </rule>
    <rule ref="Squiz.Strings.DoubleQuoteUsage">
        <exclude name="Squiz.Strings.DoubleQuoteUsage.ContainsVar"/>
    </rule>
    <rule ref="Squiz.Strings.EchoedStrings"/>
    <rule ref="Squiz.WhiteSpace.CastSpacing"/>
    <rule ref="Squiz.WhiteSpace.FunctionOpeningBraceSpace"/>
    <rule ref="Squiz.WhiteSpace.FunctionSpacing">
        <properties>
            <property name="spacing" value="1"/>
        </properties>
    </rule>
    <rule ref="Squiz.WhiteSpace.LanguageConstructSpacing"/>
    <rule ref="Squiz.WhiteSpace.LogicalOperatorSpacing"/>
    <rule ref="Squiz.WhiteSpace.MemberVarSpacing"/>
    <rule ref="Squiz.WhiteSpace.ObjectOperatorSpacing">
        <properties>
            <property name="ignoreNewlines" value="true"/>
        </properties>
    </rule>
    <rule ref="Squiz.WhiteSpace.OperatorSpacing">
        <properties>
            <property name="ignoreNewlines" value="true"/>
        </properties>
    </rule>
    <rule ref="Squiz.WhiteSpace.SemicolonSpacing"/>
    <rule ref="Squiz.WhiteSpace.SuperfluousWhitespace">
        <properties>
            <property name="ignoreBlankLines" value="false"/><!-- turned on by PSR2 -> turning off to be more general -->
        </properties>
    </rule>
    <rule ref="Squiz.WhiteSpace.SuperfluousWhitespace.EmptyLines">
        <severity>5</severity><!-- turned off by PSR2 -> turning on with default severity -->
    </rule>

    <rule ref="SlevomatCodingStandard.Arrays.ArrayAccess"/>
    <rule ref="SlevomatCodingStandard.Arrays.DisallowPartiallyKeyed"/>
    <rule ref="SlevomatCodingStandard.Arrays.MultiLineArrayEndBracketPlacement"/>
    <rule ref="SlevomatCodingStandard.Arrays.TrailingArrayComma"/>
    <rule ref="SlevomatCodingStandard.Arrays.SingleLineArrayWhitespace"/>
    <rule ref="SlevomatCodingStandard.Attributes.AttributeAndTargetSpacing"/>
    <rule ref="SlevomatCodingStandard.Attributes.DisallowMultipleAttributesPerLine"/>
    <rule ref="SlevomatCodingStandard.Attributes.DisallowAttributesJoining"/>
    <rule ref="SlevomatCodingStandard.Attributes.RequireAttributeAfterDocComment"/>
    <rule ref="SlevomatCodingStandard.Classes.BackedEnumTypeSpacing"/>
    <rule ref="SlevomatCodingStandard.Classes.ModernClassNameReference"/>
    <rule ref="SlevomatCodingStandard.Classes.ClassConstantVisibility"/>
    <rule ref="SlevomatCodingStandard.Classes.ClassStructure">
        <properties>
            <property name="groups" type="array">
                <element value="uses"/>
                <element value="enum cases"/>
                <element value="constants"/>
                <element value="properties"/>
                <element value="constructor"/>
                <element value="destructor"/>
                <element value="methods"/>
            </property>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Classes.UselessLateStaticBinding"/>
    <rule ref="SlevomatCodingStandard.Classes.DisallowLateStaticBindingForConstants"/>
    <rule ref="SlevomatCodingStandard.Classes.DisallowMultiConstantDefinition"/>
    <rule ref="SlevomatCodingStandard.Classes.DisallowMultiPropertyDefinition"/>
    <rule ref="SlevomatCodingStandard.Classes.DisallowStringExpressionPropertyFetch"/>
    <rule ref="SlevomatCodingStandard.Classes.EnumCaseSpacing">
        <properties>
            <property name="minLinesCountBeforeWithComment" value="0"/>
            <property name="maxLinesCountBeforeWithComment" value="1"/>
            <property name="minLinesCountBeforeWithoutComment" value="0"/>
            <property name="maxLinesCountBeforeWithoutComment" value="1"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Classes.RequireSelfReference"/>
    <rule ref="SlevomatCodingStandard.Classes.RequireMultiLineMethodSignature">
        <properties>
            <property name="minParametersCount" value="7"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Classes.MethodSpacing"/>
    <rule ref="SlevomatCodingStandard.Classes.EmptyLinesAroundClassBraces"/>
    <rule ref="SlevomatCodingStandard.Classes.TraitUseDeclaration"/>
    <rule ref="SlevomatCodingStandard.Classes.TraitUseSpacing"/>
    <rule ref="SlevomatCodingStandard.Classes.PropertyDeclaration"/>
    <rule ref="SlevomatCodingStandard.Commenting.AnnotationName"/>
    <rule ref="SlevomatCodingStandard.Commenting.DocCommentSpacing">
        <properties>
            <property name="linesCountBeforeFirstContent" value="0"/>
            <property name="linesCountAfterLastContent" value="0"/>
            <property name="linesCountBetweenDescriptionAndAnnotations" value="1"/>
            <property name="linesCountBetweenAnnotationsGroups" value="1"/>
            <property name="annotationsGroups" type="array">
                <element value="
                    @Get,
                    @Post,
                    @Put,
                    @Patch,
                    @Delete,
                "/>
                <element value="
                    @QueryParam,
                "/>
                <element value="
                    @var,
                    @param,
                    @return,
                "/>
                <element value="
                    @template,
                    @extends,
                    @implements,
                "/>
            </property>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Commenting.EmptyComment"/>
    <rule ref="SlevomatCodingStandard.Commenting.DisallowOneLinePropertyDocComment"/>
    <rule ref="SlevomatCodingStandard.Commenting.InlineDocCommentDeclaration">
        <properties>
            <property name="allowAboveNonAssignment" value="true"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Commenting.UselessInheritDocComment"/>
    <rule ref="SlevomatCodingStandard.Commenting.UselessFunctionDocComment"/>
    <rule ref="SlevomatCodingStandard.Commenting.ForbiddenComments">
        <properties>
            <property name="forbiddenCommentPatterns" type="array" value="~^[a-zA-Z0-9]+ constructor.?$~,~PhpStorm~,~^[GS]et [a-zA-Z0-9]+$~"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Commenting.ForbiddenAnnotations">
        <properties>
            <property
                name="forbiddenAnnotations"
                type="array"
                value="
                    @author,@created,@version,@package,@copyright,@license,@since,@link,@group,@expectedException,
                    @dataProvider,@before,@after,@doesNotPerformAssertions,
                    @internal-allowed-scope,@internal-allowed-path,
                    @phpstan-return,@phpstan-var,@phpstan-param,@phpstan-template,@phpstan-extends,@phpstan-implements,
                    @psalm-var,@psalm-param,@psalm-type,@psalm-return,@psalm-template,
                    @covers"
            />
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.ControlStructures.AssignmentInCondition"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.LanguageConstructWithParentheses"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.DisallowYodaComparison"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.DisallowTrailingMultiLineTernaryOperator"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.NewWithParentheses"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.RequireNullCoalesceEqualOperator"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.RequireNullCoalesceOperator"/>
    <rule ref="SlevomatCodingStandard.Exceptions.DeadCatch"/>
<!--    <rule ref="SlevomatCodingStandard.Exceptions.DisallowNonCapturingCatch"/>-->
    <rule ref="SlevomatCodingStandard.Exceptions.ReferenceThrowableOnly">
        <exclude name="SlevomatCodingStandard.Exceptions.ReferenceThrowableOnly.ReferencedGeneralException"/> <!-- TODO remove in future -->
    </rule>

    <rule ref="SlevomatCodingStandard.Functions.DisallowArrowFunction"/>
    <rule ref="SlevomatCodingStandard.Functions.DisallowTrailingCommaInClosureUse">
        <properties>
            <property name="onlySingleLine" value="true"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Functions.DisallowTrailingCommaInDeclaration">
        <properties>
            <property name="onlySingleLine" value="true"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Functions.DisallowTrailingCommaInCall">
        <properties>
            <property name="onlySingleLine" value="true"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Functions.NamedArgumentSpacing"/>
    <rule ref="SlevomatCodingStandard.Functions.RequireTrailingCommaInClosureUse"/>
    <rule ref="SlevomatCodingStandard.Functions.RequireTrailingCommaInDeclaration"/>
    <rule ref="SlevomatCodingStandard.Functions.RequireTrailingCommaInCall"/>
    <rule ref="SlevomatCodingStandard.Functions.StaticClosure"/>
    <rule ref="SlevomatCodingStandard.Functions.UselessParameterDefaultValue"/>
    <rule ref="SlevomatCodingStandard.Functions.UnusedInheritedVariablePassedToClosure"/>
    <rule ref="SlevomatCodingStandard.Files.TypeNameMatchesFileName">
        <properties>
            <property
                name="rootNamespaces"
                type="array"
                value="
                    migrations=>DoctrineMigrations,
                    build/PHPStan/tests=>App\Rules\PHPStan\Tests,
                    build=>App\Rules,
                    src=>Pm,
                    src/OpenApi=>OpenApi,
                    tests=>Pm\Tests,
            "/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Functions.DisallowArrowFunction"/>
    <rule ref="SlevomatCodingStandard.Namespaces.UseFromSameNamespace"/>
    <rule ref="SlevomatCodingStandard.Namespaces.AlphabeticallySortedUses">
        <properties>
            <property name="caseSensitive" value="false"/>
            <property name="psr12Compatible" value="true"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Namespaces.DisallowGroupUse"/>
    <rule ref="SlevomatCodingStandard.Namespaces.NamespaceDeclaration"/>
    <rule ref="SlevomatCodingStandard.Namespaces.NamespaceSpacing"/>
    <rule ref="SlevomatCodingStandard.Namespaces.RequireOneNamespaceInFile"/>
    <rule ref="SlevomatCodingStandard.Namespaces.MultipleUsesPerLine"/>
    <rule ref="SlevomatCodingStandard.Namespaces.UseDoesNotStartWithBackslash"/>
    <rule ref="SlevomatCodingStandard.Namespaces.UseSpacing"/>
    <rule ref="SlevomatCodingStandard.Namespaces.UselessAlias"/>
    <rule ref="SlevomatCodingStandard.Namespaces.UnusedUses">
        <properties>
            <property name="searchAnnotations" value="true"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Namespaces.ReferenceUsedNamesOnly">
        <properties>
            <property name="allowFullyQualifiedGlobalFunctions" value="false"/>
            <property name="allowFullyQualifiedNameForCollidingFunctions" value="false"/>
            <property name="allowFallbackGlobalFunctions" value="false"/>

            <property name="allowFullyQualifiedGlobalConstants" value="false"/>
            <property name="allowFullyQualifiedNameForCollidingConstants" value="false"/>
            <property name="allowFallbackGlobalConstants" value="false"/>

            <property name="allowPartialUses" value="false"/>
            <property name="searchAnnotations" value="true"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Namespaces.ReferenceUsedNamesOnly.PartialUse">
        <exclude-pattern>config/bundles.php</exclude-pattern>
    </rule>
    <rule ref="SlevomatCodingStandard.Numbers.DisallowNumericLiteralSeparator" />
    <rule ref="SlevomatCodingStandard.Operators.DisallowEqualOperators"/>
    <rule ref="SlevomatCodingStandard.Operators.SpreadOperatorSpacing"/>
    <rule ref="SlevomatCodingStandard.PHP.DisallowDirectMagicInvokeCall"/>
    <rule ref="SlevomatCodingStandard.PHP.ShortList"/>
    <rule ref="SlevomatCodingStandard.PHP.TypeCast"/>
    <rule ref="SlevomatCodingStandard.PHP.UselessSemicolon"/>
    <rule ref="SlevomatCodingStandard.PHP.OptimizedFunctionsWithoutUnpacking"/>
    <rule ref="SlevomatCodingStandard.PHP.ReferenceSpacing"/>
    <rule ref="SlevomatCodingStandard.PHP.RequireNowdoc"/>
    <rule ref="SlevomatCodingStandard.TypeHints.NullableTypeForNullDefaultValue"/>
    <rule ref="SlevomatCodingStandard.TypeHints.NullTypeHintOnLastPosition"/>
    <rule ref="SlevomatCodingStandard.TypeHints.UselessConstantTypeHint"/>
    <rule ref="SlevomatCodingStandard.Strings.DisallowVariableParsing"/>
    <rule ref="SlevomatCodingStandard.Functions.StrictCall"/>
    <rule ref="SlevomatCodingStandard.TypeHints.DeclareStrictTypes">
        <properties>
            <property name="declareOnFirstLine" value="false"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.TypeHints.PropertyTypeHint">
        <properties>
            <property name="enableNativeTypeHint" value="true"/>
            <property name="traversableTypeHints" type="array" value="
                Generator,
                Traversable,
                Doctrine\Common\Collections\Collection,
                Doctrine\Common\Collections\ArrayCollection
            "/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.TypeHints.ParameterTypeHint">
        <properties>
            <property name="traversableTypeHints" type="array" value="
                Generator,
                Traversable,
                Doctrine\Common\Collections\Collection,
                Doctrine\Common\Collections\ArrayCollection
            "/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.TypeHints.ReturnTypeHint">
        <properties>
            <property name="traversableTypeHints" type="array" value="
                Generator,
                Traversable,
                Doctrine\Common\Collections\Collection,
                Doctrine\Common\Collections\ArrayCollection
            "/>
            <property name="enableStaticTypeHint" value="true"/>
            <property name="enableMixedTypeHint" value="true"/>
            <property name="enableUnionTypeHint" value="true"/>
            <property name="enableIntersectionTypeHint" value="true"/>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.TypeHints.NullableTypeForNullDefaultValue"/>
    <rule ref="SlevomatCodingStandard.TypeHints.NullTypeHintOnLastPosition"/>

    <rule ref="SlevomatCodingStandard.TypeHints.LongTypeHints"/>
    <rule ref="SlevomatCodingStandard.TypeHints.ReturnTypeHintSpacing"/>
    <rule ref="SlevomatCodingStandard.TypeHints.ParameterTypeHintSpacing"/>
    <rule ref="SlevomatCodingStandard.Variables.DisallowVariableVariable"/>
    <rule ref="SlevomatCodingStandard.Variables.DuplicateAssignmentToVariable"/>
    <rule ref="SlevomatCodingStandard.Variables.UnusedVariable">
        <properties>
            <property name="ignoreUnusedValuesWhenOnlyKeysAreUsedInForeach" value="true"/>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.Variables.UselessVariable"/>
    <rule ref="SlevomatCodingStandard.Whitespaces.DuplicateSpaces"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.AssignmentInCondition"/>
    <!--    <rule ref="SlevomatCodingStandard.ControlStructures.BlockControlStructureSpacing"/> Maybe later -->
    <rule ref="SlevomatCodingStandard.ControlStructures.DisallowContinueWithoutIntegerOperandInSwitch"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.UselessIfConditionWithReturn"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.UselessTernaryOperator"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.EarlyExit">
        <exclude name="SlevomatCodingStandard.ControlStructures.EarlyExit.EarlyExitNotUsed"/>
    </rule>
</ruleset>
