get_container = $(firstword $(container) backend)

builddev:
	docker compose stop
	APP_ENV=dev docker compose build $(container)
	docker compose stop

buildprod:
	docker compose stop
	APP_ENV=prod docker compose build $(container)
	docker compose stop

logs:
	docker compose logs $(get_container)

up:
	APP_ENV=dev docker compose up -d $(container) --remove-orphans

stop:
	docker compose stop $(container)

down:
	docker compose down $(container)

restart: down up

bash:
	docker compose exec $(get_container) bash

install:
	docker compose exec $(get_container) bash -c 'composer install'

test: up
	docker compose exec $(get_container) bash -c 'composer check:tests'

fix:
	docker compose exec $(get_container) bash -c 'composer fix'

check:
	docker compose exec $(get_container) bash -c 'composer check'

setup:
	docker compose exec $(get_container) bash -c 'bin/console lexik:jwt:generate-keypair && php bin/console assets:install'

export-docs:
	docker compose exec $(get_container) bash -c 'composer generate:open-api-doc'

migrate:
	docker compose exec $(get_container) bash -c 'bin/console doctrine:migrations:migrate --no-interaction'

fixtures:
	docker compose exec $(get_container) bash -c 'bin/console doctrine:fixtures:load --no-interaction'

recreate-db:
	docker compose exec $(get_container) bash -c 'composer prepare-database'

recreate-test-db:
	docker compose exec $(get_container) bash -c 'composer prepare-test-database'
