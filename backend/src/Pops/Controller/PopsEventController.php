<?php

declare(strict_types = 1);

namespace Pm\Pops\Controller;

use DateMalformedStringException;
use Doctrine\ORM\Exception\ORMException;
use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use League\Flysystem\FilesystemException;
use League\Flysystem\FilesystemOperator;
use OpenApi\Attributes\MediaType;
use OpenApi\Attributes\Parameter;
use OpenApi\Attributes\Response as OpenApiResponse;
use OpenApi\Attributes\Schema;
use OpenApi\Attributes\Tag;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Enum\FileTypeEnum;
use Pm\Core\Exception\AclException;
use Pm\Core\OpenApi\DeletedResponse;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\IdParameter;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\Core\Security\Security;
use Pm\EventCategory\Exception\NoEventCategoryFoundException;
use Pm\Pops\Enum\TemplatePdfEnum;
use Pm\Pops\Exception\NoPopsAttachmentFoundException;
use Pm\Pops\Exception\NoPopsEventFoundException;
use Pm\Pops\Exception\NoPopsFoundException;
use Pm\Pops\Exception\PopsEventException;
use Pm\Pops\Export\PdfFactory;
use Pm\Pops\Export\SpreadsheetFactory;
use Pm\Pops\Facade\PopsEventFacade;
use Pm\Pops\Filters\PopsEventListFilters;
use Pm\Pops\HttpResponse\PopsEventCategorySummaryHttpResponse;
use Pm\Pops\HttpResponse\PopsEventHttpResponse;
use Pm\Pops\Input\PopsEventCreateInput;
use Pm\Pops\Input\PopsEventGetInput;
use Pm\Pops\Input\PopsEventUpdateInput;
use Pm\Pops\Repository\PopsRepository;
use Pm\Tools\Exception\ImageOptimizationException;
use Pm\Tools\Mime\MimeType;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Error\SyntaxError;
use function iconv;
use function is_string;

#[Tag(name: 'PopsEvent')]
class PopsEventController extends BaseController
{

    public function __construct(
        private readonly PopsEventFacade $popsEventFacade,
        private readonly PopsRepository $popsRepository,
        private readonly FilesystemOperator $storage,
        private readonly SpreadsheetFactory $spreadsheetFactory,
        private readonly PdfFactory $pdfFactory,
    )
    {
    }

    /**
     * @throws AclException
     * @throws NoPopsEventFoundException
     */
    #[DetailResponse(PopsEventHttpResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Get(path: '/api/v1/pops-event/{id}')]
    public function getAction(
        int $id,
    ): SuccessOutput
    {
        $popsEvent = $this->popsEventFacade->getPopsEvent($id);

        return new SuccessOutput($popsEvent);
    }

    /**
     * @throws AclException
     * @throws NoPopsFoundException
     */
    #[ListResponse(PopsEventHttpResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[Get(path: '/api/v1/pops-event')]
    public function getListAction(
        #[MapQueryString]
        PopsEventGetInput $popsEventGetInput,
    ): SuccessCountableOutput
    {
        $popsEvents = $this->popsEventFacade->getPopsEventPaginatedListResponse($popsEventGetInput);

        /**
         * @var list<PopsEventHttpResponse> $popsEventArray
         */
        $popsEventArray = $popsEvents->getArrayCopy();
        return new SuccessCountableOutput($popsEventArray, $popsEvents->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws NoPopsFoundException
     * @throws LoaderError
     * @throws RuntimeError
     * @throws SyntaxError
     */
    #[OpenApiResponse(
        response: 200,
        description: 'PDF file',
        content: new MediaType(
            mediaType: 'application/pdf',
        ),
    )]
    #[ForbiddenResponse]
    #[Get(path: '/api/v1/pops-event-pdf')]
    public function getPdfListAction(
        #[MapQueryString]
        PopsEventGetInput $popsEventGetInput,
        Security $security,
    ): Response
    {
        $startsAt = null;
        $endsAt = null;
        $facility = null;
        $author = null;

        if ($popsEventGetInput->popsId !== null) {
            $pops = $this->popsRepository->get($popsEventGetInput->popsId);
            $startsAt = $pops->getStartsAt();
            $endsAt = $pops->getEndsAt();
            $facility = $pops->getFacility();
            $author = $pops->getInsertedBy();
        }

        $popsEventListFilters = new PopsEventListFilters();
        $popsEventListFilters->setEventCategoryHidden(false);

        $popsEvents = $this->popsEventFacade->getPopsEventListHttpResponse($this->popsEventFacade->cleanPopsEventGetInputForExports($popsEventGetInput), $popsEventListFilters);

        // PDF mime type response
        return $this->createPdfResponse(
            $this->popsEventFacade->createExportFilename(FileTypeEnum::PDF, $facility),
            $this->pdfFactory->create(
                TemplatePdfEnum::POPSEVENTS,
                $popsEvents,
                $startsAt,
                $endsAt,
                $author,
                $facility,
            ),
        );
    }

    /**
     * @throws AclException
     * @throws NoPopsFoundException
     */
    #[ForbiddenResponse]
    #[OpenApiResponse(
        response: 200,
        description: 'XLS file',
        content: new MediaType(
            mediaType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ),
    )]
    #[Get(path: '/api/v1/pops-event-xls')]
    public function getListXlsAction(
        #[MapQueryString]
        PopsEventGetInput $popsEventGetInput,
        Security $security,
    ): Response
    {
        $startsAt = null;
        $endsAt = null;
        $facility = null;
        $author = null;
        if ($popsEventGetInput->popsId !== null) {
            $pops = $this->popsRepository->get($popsEventGetInput->popsId);
            $startsAt = $pops->getStartsAt();
            $endsAt = $pops->getEndsAt();
            $facility = $pops->getFacility();
            $author = $pops->getInsertedBy();
        }

        $popsEventListFilters = new PopsEventListFilters();
        $popsEventListFilters->setEventCategoryHidden(false);

        $popsEvents = $this->popsEventFacade->getPopsEventListHttpResponse($this->popsEventFacade->cleanPopsEventGetInputForExports($popsEventGetInput), $popsEventListFilters);

        return $this->createSpreadsheetResponse(
            $this->popsEventFacade->createExportFilename(FileTypeEnum::XLSX, $facility),
            $this->spreadsheetFactory->create(
                $popsEvents,
                $startsAt,
                $endsAt,
                $author,
                $facility,
            ),
        );
    }

    /**
     * @throws AclException
     * @throws NoPopsFoundException
     */
    #[DetailResponse(PopsEventCategorySummaryHttpResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[Get(path: '/api/v1/pops-event-summary')]
    public function getListSummaryAction(
        #[MapQueryString]
        PopsEventGetInput $popsEventGetInput,
    ): SuccessOutput
    {
        $popsEvents = $this->popsEventFacade->getPopsEventListSummary($popsEventGetInput);

        return new SuccessOutput($popsEvents);
    }

    /**
     * @throws AclException
     * @throws NoPopsFoundException
     * @throws NoEventCategoryFoundException|PopsEventException|ORMException|ImageOptimizationException|FilesystemException|DateMalformedStringException
     */
    #[DetailResponse(PopsEventHttpResponse::class, 'pops-event-created', Response::HTTP_CREATED)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[Post(path: '/api/v1/pops-event')]
    public function createPopsEventAction(
        #[MapRequestPayload]
        PopsEventCreateInput $request,
        Request $rawRequest,
    ): SuccessOutput
    {
        /** @var array<UploadedFile> $files */
        $files = $rawRequest->files->all();

        return new SuccessOutput(
            $this->popsEventFacade->createPopsEvent($request, $files),
            Response::HTTP_CREATED,
            'pops-event-created',
        );
    }

    /**
     * @throws AclException
     * @throws NoPopsEventFoundException
     * @throws PopsEventException|ORMException|ImageOptimizationException|FilesystemException
     */
    #[DetailResponse(PopsEventHttpResponse::class, 'pops-event-updated')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Put(path: '/api/v1/pops-event/{id}')]
    public function updatePopsAction(
        int $id,
        #[MapRequestPayload]
        PopsEventUpdateInput $request,
        Request $rawRequest,
    ): SuccessOutput
    {
        /** @var array<UploadedFile> $files */
        $files = $rawRequest->files->all();

        return new SuccessOutput(
            $this->popsEventFacade->updatePopsEvent($id, $request, $files),
            Response::HTTP_OK,
            'pops-event-updated',
        );
    }

    /**
     * @throws AclException
     * @throws NoPopsEventFoundException|PopsEventException|UnprocessableEntityHttpException|ORMException|ImageOptimizationException|FilesystemException
     */
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[DetailResponse(PopsEventHttpResponse::class, 'pops-attachment-uploaded')]
    #[Post(path: '/api/v1/pops-event/{id}/upload-attachment')]
    public function uploadAttachmentPopsAction(
        int $id,
        Request $request,
    ): SuccessOutput
    {
        /** @var array<UploadedFile> $files */
        $files = $request->files->all();

        return new SuccessOutput(
            $this->popsEventFacade->uploadAttachmentPopsEvent($id, $files),
            Response::HTTP_OK,
            'pops-attachment-uploaded',
        );
    }

    /**
     * @throws AclException
     * @throws NoPopsEventFoundException
     */
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Parameter(
        name: 'attachmentId',
        in: 'path',
        required: true,
        schema: new Schema(type: 'integer'),
    )]
    #[DeletedResponse(message: 'pops-attachment-deleted')]
    #[Delete(path: '/api/v1/pops-event/{id}/delete-attachment/{attachmentId}')]
    public function deleteAttachmentPopsAction(
        int $id,
        int $attachmentId,
    ): SuccessOutput
    {
        $this->popsEventFacade->deleteAttachmentPopsEvent($id, $attachmentId);

        return new SuccessOutput(
            [],
            Response::HTTP_OK,
            'pops-attachment-deleted',
        );
    }

    /**
     * @throws AclException
     * @throws NoPopsEventFoundException
     * @throws NoPopsAttachmentFoundException
     */
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Parameter(
        name: 'path',
        in: 'path',
        required: true,
        schema: new Schema(type: 'string'),
    )]
    #[OpenApiResponse(
        response: 200,
        description: 'Attachment file',
        content: new MediaType(
            mediaType: 'application/octet-stream',
        ),
    )]
    #[Get(path: '/api/v1/pops-event/{id}/attachment/{path}')]
    public function getAttachmentPopsAction(
        int $id,
        string $path,
    ): Response
    {
        // permision to view are checked in facade
        $attachment = $this->popsEventFacade->getPopsEventAttachment($id, $path);

        $data = $this->storage->read($attachment->getPath());

        $mimeType = $this->storage->mimeType($attachment->getPath());

        $response = new Response($data);

        $response->headers->set('Content-Type', $mimeType);

        $filename = $attachment->getName();

        $sanitizedFilename = iconv('UTF-8', 'ASCII//TRANSLIT', $filename);
        if (!is_string($sanitizedFilename)) {
            $extension = '';
            $mimeTypeEnum = MimeType::tryFrom($mimeType);
            if ($mimeTypeEnum instanceof MimeType) {
                $extension = '.' . $mimeTypeEnum->getExtension();
            }
            $filenameFallback = "pops-event-{$id}-{$attachment->getId()}{$extension}";
        } else {
            $filenameFallback = $sanitizedFilename;
        }

        $response->headers->set('Content-Disposition', HeaderUtils::makeDisposition(
            HeaderUtils::DISPOSITION_ATTACHMENT,
            $filename,
            $filenameFallback,
        ));

        return $response;
    }

}
