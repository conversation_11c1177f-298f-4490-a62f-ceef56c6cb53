<?php

declare(strict_types = 1);

namespace Pm\Pops\Controller;

use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use OpenApi\Attributes\Tag;
use Pm\Account\Exception\NoAccountFoundException;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\IdParameter;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\Facility\Exception\NoFacilityFoundException;
use Pm\Pops\Exception\NoPopsFoundException;
use Pm\Pops\Exception\PopsIsLockedException;
use Pm\Pops\Facade\PopsFacade;
use Pm\Pops\HttpResponse\PopsHttpResponse;
use Pm\Pops\Input\PopsCreateUpdateInput;
use Pm\Pops\Input\PopsGetInput;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Throwable;

#[Tag(name: 'Pops')]
class PopsController extends BaseController
{

    public function __construct(
        private readonly PopsFacade $popsFacade,
    )
    {
    }

    /**
     * @throws AclException
     * @throws NoAccountFoundException
     * @throws NoFacilityFoundException
     * @throws InvalidInputException
     */
    #[ListResponse(PopsHttpResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[Get(path: '/api/v1/pops')]
    public function getListAction(
        #[MapQueryString]
        ?PopsGetInput $popsGetInput,
    ): SuccessCountableOutput
    {
        if ($popsGetInput === null) {
            $popsGetInput = new PopsGetInput();
        }

        $popsList = $this->popsFacade->getPopsPaginatedListResponse($popsGetInput);

        /**
         * @var list<PopsHttpResponse> $list
         */
        $list = $popsList->getArrayCopy();
        return new SuccessCountableOutput($list, $popsList->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws NoPopsFoundException
     */
    #[DetailResponse(PopsHttpResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Get(path: '/api/v1/pops/{id}')]
    public function getDetailAction(
        int $id,
    ): SuccessOutput
    {
        $pops = $this->popsFacade->getPops($id);

        return new SuccessOutput($pops);
    }

    /**
     * @throws Throwable
     */
    #[DetailResponse(PopsHttpResponse::class, 'pops-created', Response::HTTP_CREATED)]
    #[ForbiddenResponse]
    #[Post(path: '/api/v1/pops')]
    public function createPopsAction(
        #[MapRequestPayload]
        PopsCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->popsFacade->createPops($request),
            Response::HTTP_CREATED,
            'pops-created',
        );
    }

    /**
     * @throws AclException
     * @throws NoPopsFoundException
     * @throws InvalidInputException
     * @throws PopsIsLockedException
     */
    #[DetailResponse(PopsHttpResponse::class, 'pops-updated')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Put(path: '/api/v1/pops/{id}')]
    public function updatePopsAction(
        int $id,
        #[MapRequestPayload]
        PopsCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->popsFacade->updatePops($id, $request),
            Response::HTTP_OK,
            'pops-updated',
        );
    }

    #[DetailResponse(PopsHttpResponse::class, 'pops-locked')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Put(path: '/api/v1/pops/{id}/lock')]
    public function lockPopsAction(
        int $id,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->popsFacade->lockPops($id),
            Response::HTTP_OK,
            'pops-locked',
        );
    }

}
