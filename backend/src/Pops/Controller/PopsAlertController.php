<?php

declare(strict_types = 1);

namespace Pm\Pops\Controller;

use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Put;
use OpenApi\Attributes\Tag;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\Pops\Exception\NoPopsAlertFoundException;
use Pm\Pops\Facade\PopsAlertFacade;
use Pm\Pops\HttpResponse\PopsAlertHttpResponse;
use Pm\Pops\Input\PopsAlertAcknowledgeInput;
use Pm\User\Entity\User;
use Pm\User\Facade\UserFacade;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use function count;

#[Tag(name: 'PopsAlert')]
class PopsAlertController extends BaseController
{

    public function __construct(
        private readonly PopsAlertFacade $popsAlertFacade,
        private readonly UserFacade $userFacade,
    )
    {
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[ListResponse(PopsAlertHttpResponse::class)]
    #[ForbiddenResponse]
    #[Get(path: '/api/v1/pops-alert/active')]
    public function getActiveAlertsAction(
        #[CurrentUser]
        User $user,
    ): SuccessCountableOutput
    {
        $user = $this->userFacade->getUser($user);

        $popsAlertList = $this->popsAlertFacade->getActiveAlertsByUserList($user);

        return new SuccessCountableOutput($popsAlertList, count($popsAlertList));
    }

    /**
     * @throws AclException|NoPopsAlertFoundException|InvalidInputException
     */
    #[DetailResponse(PopsAlertHttpResponse::class, 'pops-alert-acknowledged')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[Put(path: '/api/v1/pops-alert/acknowledge')]
    public function acknowledgePopsAlertAction(
        #[MapRequestPayload]
        PopsAlertAcknowledgeInput $request,
    ): SuccessOutput
    {
        if (count($request->popsAlerts) === 0) {
            throw new InvalidInputException('popsAlerts input cannot be empty');
        }

        foreach ($request->popsAlerts as $id) {
            $this->popsAlertFacade->acknowledgePopsAlert($id);
        }

        return new SuccessOutput(
            [],
            Response::HTTP_OK,
            'pops-alert-acknowledged',
        );
    }

}
