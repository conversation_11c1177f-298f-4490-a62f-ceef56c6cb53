<?php

declare(strict_types = 1);

namespace Pm\Facility\Controller;

use FOS\RestBundle\Controller\Annotations\Get;
use OpenApi\Attributes\Parameter;
use OpenApi\Attributes\Schema;
use OpenApi\Attributes\Tag;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\EventCategory\HttpResponse\EventCategoryResponse;
use Pm\Facility\Exception\NoFacilityFoundException;
use Pm\Facility\Facade\FavoriteEventCategoryFacade;
use function count;

#[Tag(name: 'FavoriteEventCategory')]
class FavoriteEventCategoryController extends BaseController
{

    public function __construct(
        private readonly FavoriteEventCategoryFacade $favoriteEventCategoryFacade,
    )
    {
    }

    /**
     * Get favorites event categories for facility
     *
     * @throws AclException
     * @throws NoFacilityFoundException|InvalidInputException
     */
    #[ListResponse(EventCategoryResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[Parameter(
        name: 'facilityId',
        in: 'path',
        required: true,
        schema: new Schema(type: 'integer'),
    )]
    #[Get(path: '/api/v1/facility/{facilityId}/favorite-event-category')]
    public function getListAction(int $facilityId): SuccessCountableOutput
    {
        $favoriteEventCategories = $this->favoriteEventCategoryFacade->getFavoriteEventCategoriesListResponse($facilityId);

        return new SuccessCountableOutput($favoriteEventCategories, count($favoriteEventCategories));
    }

}
