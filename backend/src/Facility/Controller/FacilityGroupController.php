<?php

declare(strict_types = 1);

namespace Pm\Facility\Controller;

use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use OpenApi\Attributes\Parameter;
use OpenApi\Attributes\Schema;
use OpenApi\Attributes\Tag;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\OpenApi\DeletedResponse;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\IdParameter;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\Facility\Exception\NoFacilityGroupFoundException;
use Pm\Facility\Facade\FacilityGroupFacade;
use Pm\Facility\HttpResponse\FacilityGroupHttpResponse;
use Pm\Facility\Input\FacilityGroupCreateInput;
use Pm\Facility\Input\FacilityGroupInput;
use Pm\Facility\Input\FacilityGroupUpdateInput;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

#[Tag(name: 'FacilityGroup')]
class FacilityGroupController extends BaseController
{

    public function __construct(
        private readonly FacilityGroupFacade $facilityGroupFacade,
    )
    {
    }

    /**
     * @throws AclException
     * @throws NoFacilityGroupFoundException
     */
    #[DetailResponse(FacilityGroupHttpResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[Parameter(
        name: 'facilityId',
        in: 'path',
        required: true,
        schema: new Schema(type: 'integer'),
    )]
    #[Get(path: '/api/v1/facility-group/{facilityId}')]
    public function getAction(
        int $facilityId,
    ): SuccessOutput
    {
        $facilityResponse = $this->facilityGroupFacade->get($facilityId);

        return new SuccessOutput($facilityResponse);
    }

    /**
     * @throws InvalidInputException
     * @throws AclException
     */
    #[ListResponse(FacilityGroupHttpResponse::class)]
    #[ForbiddenResponse]
    #[Get(path: '/api/v1/facility-group')]
    public function getListAction(
        #[MapQueryString]
        ?FacilityGroupInput $facilityGroupInput,
    ): SuccessCountableOutput
    {
        $facilityGroupList = $this->facilityGroupFacade->getFacilityGroupPaginatedListResponse(
            $facilityGroupInput ?? new FacilityGroupInput(),
        );

        /**
         * @var list<FacilityGroupHttpResponse> $list
         */
        $list = $facilityGroupList->getArrayCopy();
        return new SuccessCountableOutput($list, $facilityGroupList->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(FacilityGroupHttpResponse::class, 'facility-group-created', Response::HTTP_CREATED)]
    #[ForbiddenResponse]
    #[Post(path: '/api/v1/facility-group')]
    public function createFacilityGroupAction(
        #[MapRequestPayload]
        FacilityGroupCreateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->facilityGroupFacade->createFacilityGroup($request),
            Response::HTTP_CREATED,
            'facility-group-created',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     * @throws NoFacilityGroupFoundException
     */
    #[DetailResponse(FacilityGroupHttpResponse::class, 'facility-group-updated')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Put(path: '/api/v1/facility-group/{id}')]
    public function updateFacilityGroupAction(
        int $id,
        #[MapRequestPayload]
        FacilityGroupUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->facilityGroupFacade->updateFacilityGroup($id, $request),
            Response::HTTP_OK,
            'facility-group-updated',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     * @throws NoFacilityGroupFoundException
     */
    #[DeletedResponse(message: 'facility-group-deleted')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Delete(path: '/api/v1/facility-group/{id}')]
    public function deleteFacilityGroupAction(int $id): SuccessOutput
    {
        $this->facilityGroupFacade->deleteFacilityGroup($id);

        return new SuccessOutput(
            [],
            Response::HTTP_OK,
            'facility-group-deleted',
        );
    }

}
