<?php

declare(strict_types = 1);

namespace Pm\Tenant\Controller;

use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use OpenApi\Attributes\Parameter;
use OpenApi\Attributes\Schema;
use OpenApi\Attributes\Tag;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\OpenApi\DeletedResponse;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\IdParameter;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\Facility\Exception\NoFacilityFoundException;
use Pm\Tenant\Facade\TenantFacade;
use Pm\Tenant\HttpResponse\TenantResponse;
use Pm\Tenant\Input\TenantCreateUpdateInput;
use Pm\Tenant\Input\TenantGetInput;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

#[Tag(name: 'Tenant')]
class TenantController extends BaseController
{

    public function __construct(
        private readonly TenantFacade $tenantFacade,
    )
    {
    }

    /**
     * @throws AclException
     */
    #[ListResponse(TenantResponse::class)]
    #[ForbiddenResponse]
    #[Get(path: '/api/v1/tenant')]
    public function getListAction(
        #[MapQueryString]
        ?TenantGetInput $tenantGetInput,
    ): SuccessCountableOutput
    {
        $tenantList = $this->tenantFacade->getTenantPaginatedListResponse(
            $tenantGetInput ?? new TenantGetInput(),
        );

        /**
         * @var list<TenantResponse> $list
         */
        $list = $tenantList->getArrayCopy();
        return new SuccessCountableOutput($list, $tenantList->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(TenantResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Get(path: '/api/v1/tenant/{id}')]
    public function getDetailAction(
        int $id,
    ): SuccessOutput
    {
        $tenant = $this->tenantFacade->getTenant($id);

        return new SuccessOutput($tenant);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     * @throws NoFacilityFoundException
     */
    #[DetailResponse(TenantResponse::class, 'tenant-created', Response::HTTP_CREATED)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[Post(path: '/api/v1/tenant')]
    public function createTenantAction(
        #[MapRequestPayload]
        TenantCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->tenantFacade->createTenant($request),
            Response::HTTP_CREATED,
            'tenant-created',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     * @throws NoFacilityFoundException
     */
    #[DetailResponse(TenantResponse::class, 'tenant-updated')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Put(path: '/api/v1/tenant/{id}')]
    public function updateTenantAction(
        int $id,
        #[MapRequestPayload]
        TenantCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->tenantFacade->updateTenant($id, $request),
            Response::HTTP_OK,
            'tenant-updated',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DeletedResponse(message: 'tenant-deleted')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Delete(path: '/api/v1/tenant/{id}')]
    public function deleteTenantAction(int $id): SuccessOutput
    {
        $this->tenantFacade->deleteTenant($id);

        return new SuccessOutput(
            null,
            Response::HTTP_NO_CONTENT,
            'tenant-deleted',
        );
    }

    /**
     * @throws AclException
     * @throws NoFacilityFoundException
     */
    #[ListResponse(TenantResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[Parameter(
        name: 'facilityId',
        in: 'path',
        required: true,
        schema: new Schema(type: 'integer'),
    )]
    #[Get('/api/v1/tenant/facility/{facilityId}')]
    public function getTenantPaginatedByFacilityAction(
        int $facilityId,
        #[MapQueryString]
        ?TenantGetInput $tenantGetInput,
    ): SuccessCountableOutput
    {
        $tenantList = $this->tenantFacade->getTenantPaginatedByFacility(
            $facilityId,
            $tenantGetInput ?? new TenantGetInput(),
        );

        return new SuccessCountableOutput(
            $tenantList->getArrayCopy(),
            $tenantList->getTotalCount(),
        );
    }

}
