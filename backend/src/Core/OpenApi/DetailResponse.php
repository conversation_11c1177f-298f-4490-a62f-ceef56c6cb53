<?php

declare(strict_types = 1);

namespace Pm\Core\OpenApi;

use Attribute;
use Nelmio\ApiDocBundle\Attribute\Model;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\Response as OpenApiResponse;
use Symfony\Component\HttpFoundation\Response;

#[Attribute(Attribute::TARGET_METHOD | Attribute::IS_REPEATABLE)]
class DetailResponse extends OpenApiResponse
{

    public function __construct(string $modelClass, string $message = '', int $code = Response::HTTP_OK)
    {
        parent::__construct(
            response: $code,
            description: 'Success response.',
            content: new JsonContent(
                properties: [
                    new Property(
                        property: 'code',
                        type: 'int',
                        example: $code,
                    ),
                    new Property(
                        property: 'data',
                        ref: new Model(type: $modelClass),
                        type: 'object',
                    ),
                    new Property(
                        property: 'message',
                        type: 'string',
                        example: $message,
                    ),
                ],
            ),
        );
    }

}
