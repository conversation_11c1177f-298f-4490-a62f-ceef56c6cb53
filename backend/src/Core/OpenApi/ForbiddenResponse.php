<?php

declare(strict_types = 1);

namespace Pm\Core\OpenApi;

use Attribute;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\Response as OpenApiResponse;
use Symfony\Component\HttpFoundation\Response;

#[Attribute(Attribute::TARGET_METHOD | Attribute::IS_REPEATABLE)]
class ForbiddenResponse extends OpenApiResponse
{

    public function __construct()
    {
        parent::__construct(
            response: Response::HTTP_FORBIDDEN,
            description: 'Forbidden response.',
            content: new JsonContent(
                properties: [
                    new Property(
                        property: 'code',
                        type: 'int',
                        example: Response::HTTP_FORBIDDEN,
                    ),
                    new Property(
                        property: 'message',
                        type: 'string',
                        example: 'Access denied.',
                    ),
                ],
            ),
        );
    }

}
