<?php

declare(strict_types = 1);

namespace Pm\Core\OpenApi;

use Attribute;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\Response as OpenApiResponse;
use Symfony\Component\HttpFoundation\Response;

#[Attribute(Attribute::TARGET_METHOD | Attribute::IS_REPEATABLE)]
class NotFoundResponse extends OpenApiResponse
{

    public function __construct(string $message = 'Not found.')
    {
        parent::__construct(
            response: Response::HTTP_NOT_FOUND,
            description: 'Not found response.',
            content: new JsonContent(
                properties: [
                    new Property(
                        property: 'code',
                        type: 'int',
                        example: Response::HTTP_NOT_FOUND,
                    ),
                    new Property(
                        property: 'message',
                        type: 'string',
                        example: $message,
                    ),
                ],
            ),
        );
    }

}
