<?php

declare(strict_types = 1);

namespace Pm\Core\OpenApi;

use Attribute;
use OpenApi\Attributes\Parameter;
use OpenApi\Attributes\Schema;

#[Attribute(Attribute::TARGET_METHOD | Attribute::IS_REPEATABLE)]
class IdParameter extends Parameter
{

    public function __construct()
    {
        parent::__construct(
            name: 'id',
            in: 'path',
            required: true,
            schema: new Schema(type: 'integer'),
        );
    }

}
