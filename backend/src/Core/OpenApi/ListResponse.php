<?php

declare(strict_types = 1);

namespace Pm\Core\OpenApi;

use Attribute;
use Nelmio\ApiDocBundle\Attribute\Model;
use OpenApi\Attributes\Items;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\Response as OpenApiResponse;
use Symfony\Component\HttpFoundation\Response;

#[Attribute(Attribute::TARGET_METHOD | Attribute::IS_REPEATABLE)]
class ListResponse extends OpenApiResponse
{

    public function __construct(string $itemModelClass)
    {
        parent::__construct(
            response: Response::HTTP_OK,
            description: 'Success response.',
            content: new JsonContent(
                properties: [
                    new Property(
                        property: 'code',
                        type: 'int',
                        example: Response::HTTP_OK,
                    ),
                    new Property(
                        property: 'total_count',
                        type: 'int',
                    ),
                    new Property(
                        property: 'list',
                        type: 'array',
                        items: new Items(
                            ref: new Model(type: $itemModelClass),
                        ),
                    ),
                ],
            ),
        );
    }

}
