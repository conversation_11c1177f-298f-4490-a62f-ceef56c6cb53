<?php

declare(strict_types = 1);

namespace Pm\Customer\Controller;

use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use OpenApi\Attributes\Tag;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\OpenApi\DeletedResponse;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\IdParameter;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\Customer\Facade\CustomerFacade;
use Pm\Customer\HttpResponse\CustomerSimpleHttpResponse;
use Pm\Customer\Input\CustomerCreateUpdateInput;
use Pm\Customer\Input\CustomerGetInput;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

#[Tag(name: 'Customer')]
class CustomerController extends BaseController
{

    public function __construct(
        private readonly CustomerFacade $customerFacade,
    )
    {
    }

    /**
     * @throws AclException
     */
    #[ListResponse(CustomerSimpleHttpResponse::class)]
    #[ForbiddenResponse]
    #[Get(path: '/api/v1/customer')]
    public function getListAction(
        #[MapQueryString]
        ?CustomerGetInput $customerGetInput,
    ): SuccessCountableOutput
    {
        $customerList = $this->customerFacade->getCustomerPaginatedListResponse(
            $customerGetInput ?? new CustomerGetInput(),
        );

        /**
         * @var list<CustomerSimpleHttpResponse> $list
         */
        $list = $customerList->getArrayCopy();
        return new SuccessCountableOutput($list, $customerList->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(CustomerSimpleHttpResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Get(path: '/api/v1/customer/{id}')]
    public function getDetailAction(
        int $id,
    ): SuccessOutput
    {
        $customer = $this->customerFacade->getCustomer($id);

        return new SuccessOutput($customer);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(CustomerSimpleHttpResponse::class, 'customer-created', Response::HTTP_CREATED)]
    #[ForbiddenResponse]
    #[Post(path: '/api/v1/customer')]
    public function createCustomerAction(
        #[MapRequestPayload]
        CustomerCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->customerFacade->createCustomer($request),
            Response::HTTP_CREATED,
            'customer-created',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(CustomerSimpleHttpResponse::class, 'customer-updated')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Put(path: '/api/v1/customer/{id}')]
    public function updateCustomerAction(
        int $id,
        #[MapRequestPayload]
        CustomerCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->customerFacade->updateCustomer($id, $request),
            Response::HTTP_OK,
            'customer-updated',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DeletedResponse(message: 'customer-deleted')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Delete(path: '/api/v1/customer/{id}')]
    public function deleteCustomerAction(int $id): SuccessOutput
    {
        $this->customerFacade->deleteCustomer($id);

        return new SuccessOutput(
            null,
            Response::HTTP_NO_CONTENT,
            'customer-deleted',
        );
    }

}
