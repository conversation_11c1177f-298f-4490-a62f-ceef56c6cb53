<?php

declare(strict_types = 1);

namespace Pm\Permission\Controller;

use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use OpenApi\Attributes\Tag;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\OpenApi\DeletedResponse;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\IdParameter;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\Permission\Exception\DuplicatePermissionGroupNameException;
use Pm\Permission\Exception\NoPermissionGroupFoundException;
use Pm\Permission\Exception\NoPermissionTemplateFoundException;
use Pm\Permission\Facade\PermissionGroupFacade;
use Pm\Permission\HttpResponse\PermissionGroupHttpResponse;
use Pm\Permission\Input\PermissionGroupCreateInput;
use Pm\Permission\Input\PermissionGroupGetInput;
use Pm\Permission\Input\PermissionGroupUpdateInput;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use function count;

#[Tag(name: 'PermissionGroup')]
class PermissionGroupController extends BaseController
{

    public function __construct(
        private readonly PermissionGroupFacade $permissionGroupFacade,
    )
    {
    }

    /**
     *  Get list of permission groups
     *
     * @throws AclException
     * @throws InvalidInputException
     */
    #[ListResponse(PermissionGroupHttpResponse::class)]
    #[ForbiddenResponse]
    #[Get(path: '/api/v1/permission-group')]
    public function getListAction(
        #[MapQueryString]
        ?PermissionGroupGetInput $permissionGroupGetInput,
    ): SuccessCountableOutput
    {
        $permissionGroupList = $this->permissionGroupFacade->getPermissionGroupPaginatedListResponse(
            $permissionGroupGetInput ?? new PermissionGroupGetInput(),
        );

        /**
         * @var list<PermissionGroupHttpResponse> $list
         */
        $list = $permissionGroupList->getArrayCopy();
        return new SuccessCountableOutput($list, $permissionGroupList->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     * @throws DuplicatePermissionGroupNameException
     */
    #[DetailResponse(PermissionGroupHttpResponse::class, 'permission-group-created', Response::HTTP_CREATED)]
    #[ForbiddenResponse]
    #[Post(path: '/api/v1/permission-group')]
    public function createPermissionGroupAction(
        #[MapRequestPayload]
        PermissionGroupCreateInput $request,
    ): SuccessOutput
    {
        if (count($request->permissionTemplates) === 0) {
            throw new InvalidInputException('Permission templates are required');
        }

        if ($this->permissionGroupFacade->isPermissionGroupNameUsed($request->name)) {
            throw DuplicatePermissionGroupNameException::getByName($request->name);
        }

        return new SuccessOutput(
            $this->permissionGroupFacade->createPermissionGroup($request),
            Response::HTTP_CREATED,
            'permission-group-created',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     * @throws NoPermissionTemplateFoundException
     */
    #[DetailResponse(PermissionGroupHttpResponse::class, 'permission-group-updated')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Put(path: '/api/v1/permission-group/{id}')]
    public function updatePermissionGroupAction(
        int $id,
        #[MapRequestPayload]
        PermissionGroupUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->permissionGroupFacade->updatePermissionGroup($id, $request),
            Response::HTTP_OK,
            'permission-group-updated',
        );
    }

    /**
     * @throws AclException
     * @throws NoPermissionGroupFoundException
     */
    #[DetailResponse(PermissionGroupHttpResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Get(path: '/api/v1/permission-group/{id}')]
    public function getAction(int $id): SuccessOutput
    {
        return new SuccessOutput($this->permissionGroupFacade->get($id));
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     * @throws NoPermissionTemplateFoundException
     * @throws NoPermissionGroupFoundException
     */
    #[DeletedResponse(message: 'permission-group-deleted')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Delete(path: '/api/v1/permission-group/{id}')]
    public function deletePermissionGroupAction(int $id): SuccessOutput
    {
        $this->permissionGroupFacade->deletePermissionGroup($id);

        return new SuccessOutput(
            [],
            Response::HTTP_OK,
            'permission-group-deleted',
        );
    }

}
