<?php

declare(strict_types = 1);

namespace Pm\Permission\Controller;

use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use OpenApi\Attributes\Tag;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\OpenApi\DeletedResponse;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\IdParameter;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\Permission\Exception\DuplicateNamePermissionTemplateException;
use Pm\Permission\Exception\NoPermissionTemplateFoundException;
use Pm\Permission\Facade\PermissionTemplateFacade;
use Pm\Permission\HttpResponse\PermissionTemplateHttpResponse;
use Pm\Permission\Input\PermissionTemplateCreateInput;
use Pm\Permission\Input\PermissionTemplateGetInput;
use Pm\Permission\Input\PermissionTemplateUpdateInput;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

#[Tag(name: 'PermissionTemplate')]
class PermissionTemplateController extends BaseController
{

    public function __construct(
        private readonly PermissionTemplateFacade $permissionTemplateFacade,
    )
    {
    }

    /**
     *  Get list of permissions
     *
     * @throws AclException
     * @throws InvalidInputException
     */
    #[ListResponse(PermissionTemplateHttpResponse::class)]
    #[ForbiddenResponse]
    #[Get(path: '/api/v1/permission-template')]
    public function getListAction(
        #[MapQueryString]
        ?PermissionTemplateGetInput $permissionTemplateGetInput,
    ): SuccessCountableOutput
    {
        $permissionTemplateList = $this->permissionTemplateFacade->getPermissionTemplatePaginatedListResponse(
            $permissionTemplateGetInput ?? new PermissionTemplateGetInput(),
        );

        /**
         * @var list<PermissionTemplateHttpResponse> $arr
         */
        $arr = $permissionTemplateList->getArrayCopy();
        return new SuccessCountableOutput($arr, $permissionTemplateList->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     * @throws DuplicateNamePermissionTemplateException
     */
    #[DetailResponse(PermissionTemplateHttpResponse::class, 'permission-template-created', Response::HTTP_CREATED)]
    #[ForbiddenResponse]
    #[Post(path: '/api/v1/permission-template')]
    public function createPermissionTemplateAction(
        #[MapRequestPayload]
        PermissionTemplateCreateInput $request,
    ): SuccessOutput
    {
        if ($this->permissionTemplateFacade->isPermissionTemplateNameUsed($request->name)) {
            throw DuplicateNamePermissionTemplateException::existsByName($request->name);
        }

        return new SuccessOutput(
            $this->permissionTemplateFacade->createPermissionTemplate($request),
            Response::HTTP_CREATED,
            'permission-template-created',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     * @throws NoPermissionTemplateFoundException
     */
    #[DetailResponse(PermissionTemplateHttpResponse::class, 'permission-template-updated')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Put(path: '/api/v1/permission-template/{id}')]
    public function updatePermissionTemplateAction(
        int $id,
        #[MapRequestPayload]
        PermissionTemplateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->permissionTemplateFacade->updatePermissionTemplate($id, $request),
            Response::HTTP_OK,
            'permission-template-updated',
        );
    }

    /**
     * @throws AclException
     * @throws NoPermissionTemplateFoundException
     */
    #[DetailResponse(PermissionTemplateHttpResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Get(path: '/api/v1/permission-template/{id}')]
    public function getAction(int $id): SuccessOutput
    {
        return new SuccessOutput($this->permissionTemplateFacade->get($id));
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     * @throws NoPermissionTemplateFoundException
     */
    #[DeletedResponse(message: 'permission-template-deleted')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Delete(path: '/api/v1/permission-template/{id}')]
    public function deletePermissionTemplateAction(int $id): SuccessOutput
    {
        $this->permissionTemplateFacade->deletePermissionTemplate($id);

        return new SuccessOutput(
            [],
            Response::HTTP_OK,
            'permission-template-deleted',
        );
    }

}
