<?php

declare(strict_types = 1);

namespace Pm\EventCategory\Controller;

use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use OpenApi\Attributes\Tag;
use Pm\Account\Exception\NoAccountFoundException;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\OpenApi\DeletedResponse;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\IdParameter;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\EventCategory\Exception\NoEventCategoryFoundException;
use Pm\EventCategory\Facade\EventCategoryFacade;
use Pm\EventCategory\HttpResponse\EventCategoryResponse;
use Pm\EventCategory\Input\EventCategoryCreateInput;
use Pm\EventCategory\Input\EventCategoryInput;
use Pm\EventCategory\Input\EventCategoryUpdateInput;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryParameter;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

#[Tag(name: 'EventCategory')]
class EventCategoryController extends BaseController
{

    public function __construct(
        private readonly EventCategoryFacade $eventCategoryFacade,

    )
    {
    }

    /**
     * Get event category list
     *
     * @throws AclException|NoAccountFoundException
     */
    #[ListResponse(EventCategoryResponse::class)]
    #[ForbiddenResponse]
    #[Get(path: '/api/v1/event-category')]
    public function getEventsCategoryList(
        #[MapQueryString]
        ?EventCategoryInput $eventCategoryInput,
    ): SuccessCountableOutput
    {
        $list = $this->eventCategoryFacade->getEventsCategoryPaginatedListResponse($eventCategoryInput ?? new EventCategoryInput());

        /**
         * @var list<EventCategoryResponse> $arr
         */
        $arr = $list->getArrayCopy();
        return new SuccessCountableOutput($arr, $list->getTotalCount());
    }

    /**
     * Get event category tree
     *
     * @throws AclException|NoEventCategoryFoundException
     */
    #[DetailResponse(EventCategoryResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[Get(path: '/api/v1/event-category-tree')]
    public function getEventsCategoryTree(
        #[MapQueryParameter]
        ?string $search,
    ): SuccessOutput
    {
        return new SuccessOutput($this->eventCategoryFacade->getEventsCategoryTree($search));
    }

    /**
     * Get event category tree for translation edititing
     * independetly on app selected locale, value on entity will be returned in default locale
     * and in translation array all available translations will be returned
     *
     * @throws AclException|NoEventCategoryFoundException
     */
    #[DetailResponse(EventCategoryResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[Get(path: '/api/v1/event-category-tree-edit')]
    public function getEventsCategoryTreeEdit(
        #[MapQueryParameter]
        ?string $search,
    ): SuccessOutput
    {
        return new SuccessOutput($this->eventCategoryFacade->getEventsCategoryEditTree($search));
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(EventCategoryResponse::class, 'event-category-created', Response::HTTP_CREATED)]
    #[ForbiddenResponse]
    #[Post(path: '/api/v1/event-category')]
    public function createEventCategoryAction(
        #[MapRequestPayload]
        EventCategoryCreateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->eventCategoryFacade->create($request),
            Response::HTTP_CREATED,
            'event-category-created',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     * @throws NoEventCategoryFoundException
     */
    #[DetailResponse(EventCategoryResponse::class, 'event-category-updated')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Put(path: '/api/v1/event-category/{id}')]
    public function updateEventCategoryAction(
        int $id,
        #[MapRequestPayload]
        EventCategoryUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->eventCategoryFacade->update($id, $request),
            Response::HTTP_OK,
            'event-category-updated',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     * @throws NoEventCategoryFoundException
     */
    #[DeletedResponse(message: 'event-category-deleted')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Delete(path: '/api/v1/event-category/{id}')]
    public function deleteEventCategoryAction(int $id): SuccessOutput
    {
        $this->eventCategoryFacade->delete($id);

        return new SuccessOutput(
            [],
            Response::HTTP_OK,
            'event-category-deleted',
        );
    }

}
