<?php

declare(strict_types = 1);

namespace Pm\Config\Controller;

use FOS\RestBundle\Controller\Annotations\Get;
use OpenApi\Attributes\Tag;
use Pm\Config\HttpResponse\ConfigResponse;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\OpenApi\DetailResponse;

#[Tag(name: 'Config')]
class ConfigController extends BaseController
{

    #[DetailResponse(ConfigResponse::class)]
    #[Get(path: '/api/v1/config')]
    public function getConfigAction(): SuccessOutput
    {
        $configResponse = new ConfigResponse();

        return new SuccessOutput($configResponse);
    }

}
