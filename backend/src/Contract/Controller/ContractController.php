<?php

declare(strict_types = 1);

namespace Pm\Contract\Controller;

use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use OpenApi\Attributes\Tag;
use Pm\Contract\Facade\ContractFacade;
use Pm\Contract\HttpResponse\ContractResponse;
use Pm\Contract\Input\ContractCreateUpdateInput;
use Pm\Contract\Input\ContractGetInput;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\OpenApi\DeletedResponse;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\IdParameter;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

#[Tag(name: 'Contract')]
class ContractController extends BaseController
{

    public function __construct(
        private readonly ContractFacade $contractFacade,
    )
    {
    }

    /**
     * @throws AclException
     */
    #[ListResponse(ContractResponse::class)]
    #[ForbiddenResponse]
    #[Get(path: '/api/v1/contract')]
    public function getListAction(
        #[MapQueryString]
        ?ContractGetInput $contractGetInput,
    ): SuccessCountableOutput
    {
        $contractList = $this->contractFacade->getContractPaginatedListResponse(
            $contractGetInput ?? new ContractGetInput(),
        );

        /**
         * @var list<ContractResponse> $list
         */
        $list = $contractList->getArrayCopy();
        return new SuccessCountableOutput($list, $contractList->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(ContractResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Get(path: '/api/v1/contract/{id}')]
    public function getDetailAction(
        int $id,
    ): SuccessOutput
    {
        $contract = $this->contractFacade->getContract($id);

        return new SuccessOutput($contract);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(ContractResponse::class, 'contract-created', Response::HTTP_CREATED)]
    #[ForbiddenResponse]
    #[Post(path: '/api/v1/contract')]
    public function createContractAction(
        #[MapRequestPayload]
        ContractCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->contractFacade->createContract($request),
            Response::HTTP_CREATED,
            'contract-created',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(ContractResponse::class, 'contract-updated')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Put(path: '/api/v1/contract/{id}')]
    public function updateContractAction(
        int $id,
        #[MapRequestPayload]
        ContractCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->contractFacade->updateContract($id, $request),
            Response::HTTP_OK,
            'contract-updated',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DeletedResponse(message: 'contract-deleted')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Delete(path: '/api/v1/contract/{id}')]
    public function deleteContractAction(int $id): SuccessOutput
    {
        $this->contractFacade->deleteContract($id);

        return new SuccessOutput(
            null,
            Response::HTTP_NO_CONTENT,
            'contract-deleted',
        );
    }

}
