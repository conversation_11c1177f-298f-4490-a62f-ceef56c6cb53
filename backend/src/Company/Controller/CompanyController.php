<?php

declare(strict_types = 1);

namespace Pm\Company\Controller;

use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use OpenApi\Attributes\Tag;
use Pm\Company\Facade\CompanyFacade;
use Pm\Company\HttpResponse\CompanyResponse;
use Pm\Company\Input\CompanyCreateUpdateInput;
use Pm\Company\Input\CompanyGetInput;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\OpenApi\DeletedResponse;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\IdParameter;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

#[Tag(name: 'Company')]
class CompanyController extends BaseController
{

    public function __construct(
        private readonly CompanyFacade $companyFacade,
    )
    {
    }

    /**
     * @throws AclException
     */
    #[ListResponse(CompanyResponse::class)]
    #[ForbiddenResponse]
    #[Get(path: '/api/v1/company')]
    public function getListAction(
        #[MapQueryString]
        ?CompanyGetInput $companyGetInput,
    ): SuccessCountableOutput
    {
        $companyList = $this->companyFacade->getCompanyPaginatedListResponse(
            $companyGetInput ?? new CompanyGetInput(),
        );

        $list = $companyList->getArrayCopy();
        return new SuccessCountableOutput($list, $companyList->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(CompanyResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Get(path: '/api/v1/company/{id}')]
    public function getDetailAction(
        int $id,
    ): SuccessOutput
    {
        $company = $this->companyFacade->getCompany($id);

        return new SuccessOutput($company);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(CompanyResponse::class, 'company-created', Response::HTTP_CREATED)]
    #[ForbiddenResponse]
    #[Post(path: '/api/v1/company')]
    public function createCompanyAction(
        #[MapRequestPayload]
        CompanyCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->companyFacade->createCompany($request),
            Response::HTTP_CREATED,
            'company-created',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(CompanyResponse::class, 'company-updated')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Put(path: '/api/v1/company/{id}')]
    public function updateCompanyAction(
        int $id,
        #[MapRequestPayload]
        CompanyCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->companyFacade->updateCompany($id, $request),
            Response::HTTP_OK,
            'company-updated',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DeletedResponse(message: 'company-deleted')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Delete(path: '/api/v1/company/{id}')]
    public function deleteCompanyAction(int $id): SuccessOutput
    {
        $this->companyFacade->deleteCompany($id);

        return new SuccessOutput(
            null,
            Response::HTTP_NO_CONTENT,
            'company-deleted',
        );
    }

}
