<?php

declare(strict_types = 1);

namespace Pm\SuperPops\Security;

use Pm\Core\Exception\AclException;
use Pm\Core\Security\BaseVoter;
use Pm\Facility\Entity\Facility;
use Pm\Permission\Enum\PermissionEnum;
use Pm\Pops\Entity\PopsEntity;

readonly class SuperPopsVoter extends BaseVoter
{

    /**
     * @throws AclException
     */
    public function canViewList(Facility $facility): void
    {
        $this->canAccessByAccountId($facility->getAccount()->getId());

        if ($this->hasPermission(PermissionEnum::PERM_SUPERPOPS_SHOW)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canCreate(Facility $facility): void
    {
        $this->canAccessByAccountId($facility->getAccount()->getId());

        if ($this->hasPermission(PermissionEnum::PERM_SUPERPOPS_CREATE)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canView(PopsEntity $pops): void
    {
        $this->canAccessByAccountId($pops->getAccount()->getId());

        if ($this->hasPermission(PermissionEnum::PERM_SUPERPOPS_SHOW)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canUpdate(Facility $facility): void
    {
        $this->canAccessByAccountId($facility->getAccount()->getId());

        if ($this->hasPermission(PermissionEnum::PERM_SUPERPOPS_CREATE)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @throws AclException
     */
    public function canLock(Facility $facility): void
    {
        $this->canAccessByAccountId($facility->getAccount()->getId());

        if ($this->hasPermission(PermissionEnum::PERM_SUPERPOPS_CREATE)) {
            return;
        }

        throw new AclException();
    }

    /**
     * @return list<string>
     */
    public function listPermissions(): array
    {
        $list = [];
        if ($this->hasPermission(PermissionEnum::PERM_SUPERPOPS_CREATE)) {
            $list[] = PermissionEnum::PERM_SUPERPOPS_CREATE->value;
        }
        if ($this->hasPermission(PermissionEnum::PERM_SUPERPOPS_SHOW)) {
            $list[] = PermissionEnum::PERM_SUPERPOPS_SHOW->value;
        }

        return $list;
    }

}
