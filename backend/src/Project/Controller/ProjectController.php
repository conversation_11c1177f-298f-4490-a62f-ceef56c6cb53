<?php

declare(strict_types = 1);

namespace Pm\Project\Controller;

use FOS\RestBundle\Controller\Annotations\Delete;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Controller\Annotations\Post;
use FOS\RestBundle\Controller\Annotations\Put;
use OpenApi\Attributes\Tag;
use Pm\Core\Controller\BaseController;
use Pm\Core\Controller\SuccessCountableOutput;
use Pm\Core\Controller\SuccessOutput;
use Pm\Core\Exception\AclException;
use Pm\Core\Exception\InvalidInputException;
use Pm\Core\OpenApi\DeletedResponse;
use Pm\Core\OpenApi\DetailResponse;
use Pm\Core\OpenApi\ForbiddenResponse;
use Pm\Core\OpenApi\IdParameter;
use Pm\Core\OpenApi\ListResponse;
use Pm\Core\OpenApi\NotFoundResponse;
use Pm\Project\Facade\ProjectFacade;
use Pm\Project\HttpResponse\ProjectResponse;
use Pm\Project\Input\ProjectCreateUpdateInput;
use Pm\Project\Input\ProjectGetInput;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;

#[Tag(name: 'Project')]
class ProjectController extends BaseController
{

    public function __construct(
        private readonly ProjectFacade $projectFacade,
    )
    {
    }

    /**
     * @throws AclException
     */
    #[ListResponse(ProjectResponse::class)]
    #[ForbiddenResponse]
    #[Get(path: '/api/v1/project')]
    public function getListAction(
        #[MapQueryString]
        ?ProjectGetInput $projectGetInput,
    ): SuccessCountableOutput
    {
        $projectList = $this->projectFacade->getProjectPaginatedListResponse(
            $projectGetInput ?? new ProjectGetInput(),
        );

        /**
         * @var list<ProjectResponse> $list
         */
        $list = $projectList->getArrayCopy();
        return new SuccessCountableOutput($list, $projectList->getTotalCount());
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(ProjectResponse::class)]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Get(path: '/api/v1/project/{id}')]
    public function getDetailAction(
        int $id,
    ): SuccessOutput
    {
        $project = $this->projectFacade->getProject($id);

        return new SuccessOutput($project);
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(ProjectResponse::class, 'project-created', Response::HTTP_CREATED)]
    #[ForbiddenResponse]
    #[Post(path: '/api/v1/project')]
    public function createProjectAction(
        #[MapRequestPayload]
        ProjectCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->projectFacade->createProject($request),
            Response::HTTP_CREATED,
            'project-created',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DetailResponse(ProjectResponse::class, 'project-updated')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Put(path: '/api/v1/project/{id}')]
    public function updateProjectAction(
        int $id,
        #[MapRequestPayload]
        ProjectCreateUpdateInput $request,
    ): SuccessOutput
    {
        return new SuccessOutput(
            $this->projectFacade->updateProject($id, $request),
            Response::HTTP_OK,
            'project-updated',
        );
    }

    /**
     * @throws AclException
     * @throws InvalidInputException
     */
    #[DeletedResponse(message: 'project-deleted')]
    #[ForbiddenResponse]
    #[NotFoundResponse]
    #[IdParameter]
    #[Delete(path: '/api/v1/project/{id}')]
    public function deleteProjectAction(int $id): SuccessOutput
    {
        $this->projectFacade->deleteProject($id);

        return new SuccessOutput(
            null,
            Response::HTTP_NO_CONTENT,
            'project-deleted',
        );
    }

}
