parameters:
    doctrine.orm.entity_manager.class: Doctrine\ORM\EntityManager
    date_time_format: 'c'
    .container.dumper.inline_factories: true

services:
    _defaults:
        public: true
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: false # Automatically registers your services as commands, event subscribers, etc.
        bind:
            $container: '@service_container'

    _instanceof:
        Pm\Core\Command\BaseCommand:
            tags:
                - console.command
        Pm\Core\Controller\BaseController:
            tags:
                - controller.service_arguments
        Pm\Core\Producer\BaseProducer:
            tags: ['app.producer']

    Aws\S3\S3Client:
        arguments:
          - version: 'latest'
            region: 'eu-central-1'
            credentials:
              key: '%env(AWS_S3_ACCESS_ID)%'
              secret: '%env(AWS_S3_ACCESS_SECRET)%'
    Doctrine\ORM\EntityManager: '@doctrine.orm.entity_manager'

    GuzzleHttp\Client:

    League\MimeTypeDetection\FinfoMimeTypeDetector:
    # Log
    log.doctrine.log_entity_event_subscriber:
      class: Pm\Log\Doctrine\LogEntityEventSubscriber
      tags:
        - name: doctrine.orm.entity_listener
          entity: Pm\Log\Entity\LogEntity
          event: postPersist
        - name: doctrine.orm.entity_listener
          entity: Pm\Log\Entity\LogEntity
          event: onFlush

    Monolog\Processor\PsrLogMessageProcessor:
        tags: { name: monolog.processor, handler: sentry }

    PhpAmqpLib\Connection\AbstractConnection: '@old_sound_rabbit_mq.connection.default'

    # Account
    Pm\Account\Repository\AccountRepository:

    # AuditLog
    Pm\AuditLog\EventSubscriber\AuditLogEventSubscriber:
        tags:
            - name: 'doctrine.event_listener'
              event: 'postFlush'
            - name: 'doctrine.event_listener'
              event: 'onFlush'
              priority: 500

    Pm\AuditLog\Repository\AuditLogRepository:
    Pm\AuditLog\Service\AuditLogConfiguration:
    Pm\AuditLog\Service\AuditLogService:

    # Auth
    Pm\Auth\Controller\AuthController:
    Pm\Auth\EventSubscriber\LogOutSubscriber:
        tags: [ 'kernel.event_subscriber' ]
    Pm\Auth\EventSubscriber\UserLogInSubscriber:
        tags: [ 'kernel.event_subscriber' ]
    Pm\Auth\Facade\AuthFacade:

    # Company
    Pm\Company\Controller\CompanyController:
    Pm\Company\Facade\CompanyFacade:
    Pm\Company\Repository\CompanyRepository:
      tags: [ 'doctrine.repository_service' ]
    Pm\Company\Security\CompanyVoter:

    # Config
    Pm\Config\Controller\ConfigController:

    # Connector
    Pm\Connector\Controller\TenantMessengerController:
    Pm\Connector\Facade\EmergencyMessageFacade:

    # Contract
    Pm\Contract\Controller\ContractController:
    Pm\Contract\Facade\ContractFacade:
    Pm\Contract\Repository\ContractRepository:
      tags: [ 'doctrine.repository_service' ]
    Pm\Contract\Security\ContractVoter:

    # Core
    Pm\Core\Controller\ChangelogController:
    Pm\Core\Controller\DefaultController:
    Pm\Core\Entity\Observer\EntityObserver:
    Pm\Core\Entity\Observer\ObserverNotificationConsumer:
    Pm\Core\Entity\Observer\ObserverNotificationProducer:
    Pm\Core\EventSubscriber\ExceptionSubscriber:
        tags: [ 'kernel.event_subscriber' ]
    Pm\Core\EventSubscriber\OptionalEndpointSubscriber:
        tags: [ 'kernel.event_subscriber' ]
    Pm\Core\Http\ObserverResponseCreatorFactory:
    Pm\Core\Pm2Api\Pm2ApiClientFactory:
    Pm\Core\Pm2Api\Pm2ApiHandler:
    Pm\Core\Pm2Api\Pm2ApiRequestFactory:
    Pm\Core\Pm2Api\Pm2ApiResponseProcessor:
    Pm\Core\Security\Security:
    Pm\Core\Service\AttachmentService:
    Pm\Core\Service\TimeService:
    Pm\Core\SSE\Publisher:
    Pm\Core\SSE\TopicFactory:
    Pm\Core\Test\Builder\ObjectFactory:
    Pm\Core\Validator\EntityValidator:
        arguments:
            $validator: '@validator'

    # Currency
    Pm\Currency\Controller\CurrencyController:
    Pm\Currency\Facade\CurrencyFacade:
    Pm\Currency\Repository\CurrencyRepository:
      tags: [ 'doctrine.repository_service' ]
    Pm\Currency\Security\CurrencyVoter:

    # Customer
    Pm\Customer\Controller\CustomerController:
    Pm\Customer\Controller\CustomerUserController:
    Pm\Customer\Facade\CustomerFacade:
    Pm\Customer\Facade\CustomerUserFacade:
    Pm\Customer\Repository\CustomerRepository:
      tags: [ 'doctrine.repository_service' ]
    Pm\Customer\Repository\CustomerUserRepository:
      tags: [ 'doctrine.repository_service' ]
    Pm\Customer\Security\CustomerUserVoter:
    Pm\Customer\Security\CustomerVoter:

    # Event Category
    Pm\EventCategory\Controller\EventCategoryController:
    Pm\EventCategory\Facade\EventCategoryFacade:
    Pm\EventCategory\Repository\EventCategoryRepository:
    Pm\EventCategory\Security\EventCategoryVoter:

    # Facility
    Pm\Facility\Controller\FacilityController:
    Pm\Facility\Controller\FacilityGroupController:
    Pm\Facility\Controller\FavoriteEventCategoryController:
    Pm\Facility\EventSubscriber\FacilityEventSubscriber:
        tags: [ 'kernel.event_subscriber' ]
    Pm\Facility\Facade\FacilityFacade:
    Pm\Facility\Facade\FacilityGroupFacade:
    Pm\Facility\Facade\FavoriteEventCategoryFacade:
    Pm\Facility\Repository\FacilityGroupRepository:
        tags: [ 'doctrine.repository_service' ]
    Pm\Facility\Repository\FacilityRepository:
        tags: [ 'doctrine.repository_service' ]
    Pm\Facility\Security\FacilityGroupVoter:
    Pm\Facility\Security\FacilityVoter:
    Pm\Facility\Service\PopsEventHttpResponseCreator:
    Pm\Facility\Service\PopsHttpResponseCreator:

    #Legislation
    Pm\Legislation\Controller\LegislationController:
    Pm\Legislation\Facade\LegislationFacade:
    Pm\Legislation\Repository\LegislationRepository:
      tags: [ 'doctrine.repository_service' ]
    Pm\Legislation\Security\LegislationVoter:

    Pm\Log\Logger:
    Pm\Log\Service\LogService:

    Pm\Mobile\Repository\MobileInfoRepository:
    Pm\Mobile\Repository\MobileSettingRepository:

    # Notifier
    Pm\Notification\Consumer\EmailNotificationConsumer:
    Pm\Notification\Consumer\PushNotificationConsumer:
    Pm\Notification\Consumer\SmsNotificationConsumer:
    Pm\Notification\EventSubscriber\FailedMessageEventListener:
        tags: [ 'kernel.event_subscriber' ]
        arguments: ['%env(APP_NOTIFICATION_MAX_SEND_ATTEMPTS)%']
    Pm\Notification\EventSubscriber\SentMessageEventListener:
        tags: [ 'kernel.event_subscriber' ]
    Pm\Notification\Notifier\Bridge\SmsService\SmsServiceTransportFactory:
        tags: [texter.transport_factory]
    Pm\Notification\Producer\EmailNotificationProducer:
    Pm\Notification\Producer\PushNotificationProducer:
    Pm\Notification\Producer\SmsNotificationProducer:
    Pm\Notification\Repository\NotificationRepository:
    Pm\Notification\Service\ContentService:
    Pm\Notification\Service\NotificationService:
    Pm\Notification\Service\SendService:
        arguments: ['%env(APP_NOTIFICATION_MAX_SEND_ATTEMPTS)%']

    # Permission
    Pm\Permission\Command\CreatePermissionsCommand:
    Pm\Permission\Controller\PermissionController:
    Pm\Permission\Controller\PermissionGroupController:
    Pm\Permission\Controller\PermissionTemplateController:
    Pm\Permission\Facade\CreatePermissionsFacade:
    Pm\Permission\Facade\PermissionFacade:
    Pm\Permission\Facade\PermissionGroupFacade:
    Pm\Permission\Facade\PermissionTemplateFacade:
    Pm\Permission\Repository\PermissionApplicationRepository:
    Pm\Permission\Repository\PermissionGroupRepository:
    Pm\Permission\Repository\PermissionRepository:
    Pm\Permission\Repository\PermissionTemplateRepository:
    Pm\Permission\Security\PermissionGroupVoter:
    Pm\Permission\Security\PermissionTemplateVoter:

    # Pops
    Pm\Pops\Command\PopsAlertsCheckTimeoutCommand:
    Pm\Pops\Command\PopsCheckAttentionCommand:
    Pm\Pops\Command\PopsLockCommand:
    Pm\Pops\Controller\PopsAlertController:
    Pm\Pops\Controller\PopsController:
    Pm\Pops\Controller\PopsEventController:
    Pm\Pops\EventSubscriber\PopsEventSubscriber:
        tags: [ 'kernel.event_subscriber' ]
    Pm\Pops\EventSubscriber\PopsSubscriber:
      tags: [ 'kernel.event_subscriber' ]
    Pm\Pops\Export\PdfFactory:
    Pm\Pops\Export\SpreadsheetFactory:
    Pm\Pops\Facade\PopsAlertFacade:
      arguments:
        $defaultAlertDuration: '%env(APP_POPS_ALERT_DURATION_MINUTES)%'
        $activityAcknowledgedEventCategoryNumber: '%env(ACTIVITY_ACKNOWLEDGED_EVENT_CATEGORY_NUMBER)%'
        $activityAcknowledgedEventCategoryName: '%env(ACTIVITY_ACKNOWLEDGED_EVENT_CATEGORY_NAME)%'
        $popsEventNotCreatedCategoryNumber: '%env(POPS_EVENT_NOT_CREATED_CATEGORY_NUMBER)%'
    Pm\Pops\Facade\PopsEventFacade:
#      $filesystem: '@Aws\S3\S3Client'
    Pm\Pops\Facade\PopsFacade:
    Pm\Pops\Repository\PopsAlertRepository:
    Pm\Pops\Repository\PopsEventRepository:
    Pm\Pops\Repository\PopsRepository:
    Pm\Pops\Security\PopsEventVoter:
    Pm\Pops\Security\PopsVoter:
    Pm\Pops\Validator\DateInterval:

    # Project
    Pm\Project\Controller\ProjectController:
    Pm\Project\Facade\ProjectFacade:
    Pm\Project\Repository\ProjectRepository:
      tags: [ 'doctrine.repository_service' ]
    Pm\Project\Security\ProjectVoter:

    Pm\Reporting\Repository\ReportingSettingRepository:

  # Subject, Subject Group
    Pm\Subject\Controller\SubjectController:
    Pm\Subject\Controller\SubjectGroupController:
    Pm\Subject\Facade\SubjectFacade:
    Pm\Subject\Facade\SubjectGroupFacade:
    Pm\Subject\Repository\SubjectGroupRepository:
      tags: [ 'doctrine.repository_service' ]
    Pm\Subject\Repository\SubjectRepository:
      tags: [ 'doctrine.repository_service' ]
    Pm\Subject\Security\SubjectGroupVoter:
    Pm\Subject\Security\SubjectVoter:

    #Super Pops
    Pm\SuperPops\Command\SuperPopsLockCommand:
    Pm\SuperPops\Controller\SuperPopsController:
    Pm\SuperPops\Facade\SuperPopsFacade:
    Pm\SuperPops\Repository\SuperPopsRepository:
    Pm\SuperPops\Security\SuperPopsVoter:
    Pm\SuperPops\Service\SuperPopsHttpResponseCreator:

    # Tenant, Tenant Group
    Pm\Tenant\Controller\TenantController:
    Pm\Tenant\Controller\TenantGroupController:
    Pm\Tenant\Facade\TenantFacade:
    Pm\Tenant\Facade\TenantGroupFacade:
    Pm\Tenant\Repository\TenantGroupRepository:
      tags: [ 'doctrine.repository_service' ]
    Pm\Tenant\Repository\TenantRepository:
      tags: [ 'doctrine.repository_service' ]
    Pm\Tenant\Security\TenantGroupVoter:
    Pm\Tenant\Security\TenantVoter:

    Pm\Tools\FileSanitizer:
    Pm\Tools\ImageSanitizer:

    Pm\Tools\ServicesConfigProvider:
        $serviceConfigPath: '%kernel.project_dir%/config/services.yaml'

    # Unit
    Pm\Unit\Controller\UnitController:
    Pm\Unit\Facade\UnitFacade:
    Pm\Unit\Repository\UnitRepository:
      tags: [ 'doctrine.repository_service' ]
    Pm\Unit\Security\UnitVoter:

    # User
    Pm\User\Command\CreateDemoDataCommand:
    Pm\User\Command\DeleteDemoDataCommand:
    Pm\User\Controller\UserController:
    Pm\User\Facade\DemoAccountFacade:
    Pm\User\Facade\UserFacade:
    Pm\User\Repository\UserRepository:
        tags: [ 'doctrine.repository_service' ]
    Pm\User\Security\AccountVoter:
    Pm\User\Security\UserMigrateProvider:
    Pm\User\Security\UserVoter:
    Pm\User\Service\UserHttpResponseCreator:

    Sentry\Monolog\Handler:
      arguments:
        $hub: '@Sentry\State\HubInterface'
        $level: !php/const Monolog\Logger::ERROR

imports:
  - { resource: pm2_api.yaml }
