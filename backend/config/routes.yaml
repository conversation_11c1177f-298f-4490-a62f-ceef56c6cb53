# config/routes.yaml
AuthController:
  resource: Pm\Auth\Controller\AuthController
  type: attribute

CompanyController:
  resource: Pm\Company\Controller\CompanyController
  type: attribute

ConfigController:
  resource: Pm\Config\Controller\ConfigController
  type: attribute

ContractController:
  resource: Pm\Contract\Controller\ContractController
  type: attribute

CurrencyController:
  resource: Pm\Currency\Controller\CurrencyController
  type: attribute

CustomerController:
  resource: Pm\Customer\Controller\CustomerController
  type: attribute

CustomerUserController:
  resource: Pm\Customer\Controller\CustomerUserController
  type: attribute

UserController:
  resource: Pm\User\Controller\UserController
  type: attribute

FacilityController:
  resource: Pm\Facility\Controller\FacilityController
  type: attribute

FacilityGroupController:
  resource: Pm\Facility\Controller\FacilityGroupController
  type: attribute

FavoriteEventCategoryController:
  resource: Pm\Facility\Controller\FavoriteEventCategoryController
  type: attribute

LegislationController:
  resource: Pm\Legislation\Controller\LegislationController
  type: attribute

EventCategoryController:
  resource: Pm\EventCategory\Controller\EventCategoryController
  type: attribute

PermissionGroupController:
  resource: Pm\Permission\Controller\PermissionGroupController
  type: attribute

PermissionTemplateController:
  resource: Pm\Permission\Controller\PermissionTemplateController
  type: attribute

PopsAlertController:
  resource: Pm\Pops\Controller\PopsAlertController
  type: attribute

PopsEventController:
  resource: Pm\Pops\Controller\PopsEventController
  type: attribute

PopsController:
  resource: Pm\Pops\Controller\PopsController
  type: attribute

ProjectController:
  resource: Pm\Project\Controller\ProjectController
  type: attribute

SubjectController:
  resource: Pm\Subject\Controller\SubjectController
  type: attribute

SubjectGroupController:
  resource: Pm\Subject\Controller\SubjectGroupController
  type: attribute

SuperPopsController:
  resource: Pm\SuperPops\Controller\SuperPopsController
  type: attribute

TenantController:
  resource: Pm\Tenant\Controller\TenantController
  type: attribute

TenantGroupController:
  resource: Pm\Tenant\Controller\TenantGroupController
  type: attribute

TenantMessengerController:
  resource: Pm\Connector\Controller\TenantMessengerController
  type: attribute

PermissionController:
  resource: Pm\Permission\Controller\PermissionController
  type: attribute

UnitController:
  resource: Pm\Unit\Controller\UnitController
  type: attribute

ChangelogController:
  resource: Pm\Core\Controller\ChangelogController
  type: attribute

# controller is registered in services_test.yaml only for test environment
ApiDocController:
  resource: Pm\Core\Controller\ApiDocController
  type: attribute

default_index:
  path: '/{path}'
  defaults: { _controller: Pm\Core\Controller\DefaultController::defaultAction }
  requirements:
    path: .*
