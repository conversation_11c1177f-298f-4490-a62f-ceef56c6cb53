<?php

declare(strict_types = 1);

use Doctrine\Bundle\DoctrineBundle\DoctrineBundle;
use Doctrine\Bundle\FixturesBundle\DoctrineFixturesBundle;
use Doctrine\Bundle\MigrationsBundle\DoctrineMigrationsBundle;
use FOS\RestBundle\FOSRestBundle;
use Gesdinet\JWTRefreshTokenBundle\GesdinetJWTRefreshTokenBundle;
use JMS\SerializerBundle\JMSSerializerBundle;
use League\FlysystemBundle\FlysystemBundle;
use Lexik\Bundle\JWTAuthenticationBundle\LexikJWTAuthenticationBundle;
use Misd\PhoneNumberBundle\MisdPhoneNumberBundle;
use Nelmio\ApiDocBundle\NelmioApiDocBundle;
use Nelmio\CorsBundle\NelmioCorsBundle;
use Nucleos\DompdfBundle\NucleosDompdfBundle;
use OldSound\RabbitMqBundle\OldSoundRabbitMqBundle;
use Sentry\SentryBundle\SentryBundle;
use Stof\DoctrineExtensionsBundle\StofDoctrineExtensionsBundle;
use Symfony\Bundle\FrameworkBundle\FrameworkBundle;
use Symfony\Bundle\MercureBundle\MercureBundle;
use Symfony\Bundle\MonologBundle\MonologBundle;
use Symfony\Bundle\SecurityBundle\SecurityBundle;
use Symfony\Bundle\TwigBundle\TwigBundle;
use Symfony\Bundle\WebProfilerBundle\WebProfilerBundle;
use Twig\Extra\TwigExtraBundle\TwigExtraBundle;
use Yectep\PhpSpreadsheetBundle\PhpSpreadsheetBundle;

return [
    DoctrineBundle::class => ['all' => true],
    DoctrineFixturesBundle::class => ['dev' => true, 'test' => true],
    DoctrineMigrationsBundle::class => ['all' => true],
    FOSRestBundle::class => ['all' => true],
    GesdinetJWTRefreshTokenBundle::class => ['all' => true],
    JMSSerializerBundle::class => ['all' => true],
    LexikJWTAuthenticationBundle::class => ['all' => true],
    NelmioCorsBundle::class => ['all' => true],
    SentryBundle::class => ['all' => true],
    StofDoctrineExtensionsBundle::class => ['all' => true],
    FrameworkBundle::class => ['all' => true],
    MonologBundle::class => ['all' => true],
    SecurityBundle::class => ['all' => true],
    TwigBundle::class => ['all' => true],
    TwigExtraBundle::class => ['all' => true],
    WebProfilerBundle::class => ['dev' => true, 'test' => true],
    FlysystemBundle::class => ['all' => true],
    NucleosDompdfBundle::class => ['all' => true],
    PhpSpreadsheetBundle::class => ['all' => true],
    OldSoundRabbitMqBundle::class => ['all' => true],
    MisdPhoneNumberBundle::class => ['all' => true],
    MercureBundle::class => ['all' => true],
    NelmioApiDocBundle::class => ['all' => true],
];
