{"name": "goodsailors/pm", "description": "Pm <PERSON>", "license": "proprietary", "type": "project", "version": "1.0.0", "require": {"php": "~8.2.0", "ext-ctype": "*", "ext-iconv": "*", "ext-simplexml": "*", "composer/package-versions-deprecated": "*********", "dcarbone/php-object-merge": "^2.2", "doctrine/doctrine-bundle": "^2.12.0", "doctrine/doctrine-migrations-bundle": "^3.3.1", "doctrine/orm": "^2.19.5", "enshrined/svg-sanitize": "*", "flexic/doctrine-pg-similarity": "^1.0", "friendsofsymfony/rest-bundle": "3.8.0", "gesdinet/doctrine-functions-psql": "^0.1.0", "gesdinet/jwt-refresh-token-bundle": "^1.3.0", "guzzlehttp/guzzle": "^7.2", "jetbrains/phpstorm-attributes": "^1.1", "jms/serializer-bundle": "^5.0", "league/flysystem-aws-s3-v3": "^3.28", "league/flysystem-bundle": "^3.3", "lexik/jwt-authentication-bundle": "^3.0", "nelmio/api-doc-bundle": "^5.3", "nelmio/cors-bundle": "^2.4", "nette/caching": "^3.1", "nette/utils": "^3.2", "nucleos/dompdf-bundle": "^4.3", "odolbeau/phone-number-bundle": "^4.0", "php-amqplib/rabbitmq-bundle": "*", "phpdocumentor/reflection-docblock": "^5.4", "predis/predis": "^3.0", "prewk/xml-string-streamer": "^1.1", "sentry/sentry-symfony": "^5.0", "spatie/image-optimizer": "^1.8", "stof/doctrine-extensions-bundle": "^1.11.0", "symfony/amazon-mailer": "~7.2.0", "symfony/asset": "~7.2.0", "symfony/browser-kit": "~7.2.0", "symfony/cache": "~7.2.0", "symfony/config": "~7.2.3", "symfony/console": "~7.3.0", "symfony/dependency-injection": "7.3.0", "symfony/doctrine-bridge": "7.2.6", "symfony/dotenv": "~7.2.0", "symfony/filesystem": "7.3.0", "symfony/firebase-notifier": "~7.2.0", "symfony/flex": "^2.1", "symfony/framework-bundle": "7.2.5", "symfony/http-client": "~7.2.0", "symfony/http-kernel": "7.2.1", "symfony/intl": "~7.2.0", "symfony/lock": "~7.2.0", "symfony/mercure-bundle": "^0.3.9", "symfony/mime": "~7.3.0", "symfony/monolog-bundle": "^3.8", "symfony/process": "~7.3.0", "symfony/property-info": "7.2.5", "symfony/proxy-manager-bridge": "~6.4.0", "symfony/runtime": "~7.2.0", "symfony/security-bundle": "~7.2.0", "symfony/security-http": "~7.2.0", "symfony/serializer": "~7.2.0", "symfony/translation": "~7.2.0", "symfony/twig-bundle": "~7.2.0", "symfony/uid": "~7.3.0", "symfony/validator": "7.3.0", "symfony/yaml": "~7.2.0", "twig/extra-bundle": "^2.12 || ^3.0", "twig/twig": "^2.12 || ^3.0", "yectep/phpspreadsheet-bundle": "^1.1"}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^4.1", "editorconfig-checker/editorconfig-checker": "^10.7", "ergebnis/composer-normalize": "^2.47", "php-parallel-lint/php-parallel-lint": "^1.4", "phpdocumentor/type-resolver": "^1.10", "phpstan/phpdoc-parser": "^2.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-doctrine": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpstan/phpstan-symfony": "^2.0", "phpstan/phpstan-webmozart-assert": "^2.0", "phpunit/phpunit": "^11.5", "rector/rector": "^2.0", "shipmonk/input-mapper": "^0.9.0", "shipmonk/phpstan-rules": "^4.1", "slevomat/coding-standard": "^8.18", "squizlabs/php_codesniffer": "^3.12", "symfony/phpunit-bridge": "^7.2", "symfony/stopwatch": "^7.2", "symfony/web-profiler-bundle": "^7.2", "symplify/phpstan-rules": "^14.6"}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*"}, "conflict": {"symfony/symfony": "*"}, "minimum-stability": "stable", "prefer-stable": true, "autoload": {"psr-4": {"OpenApi\\": "src/OpenApi/", "Pm\\": "src/"}}, "autoload-dev": {"psr-4": {"Pm\\Tests\\": "tests/"}}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "ergebnis/composer-normalize": true, "php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true, "xdebug": false}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2.*"}}, "scripts": {"post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"], "auto-scripts": {"cache:clear": "symfony-cmd"}, "check": ["@check:ws", "@check:composer", "@check:cs", "@check:json", "@check:lint", "@check:types", "@check:rector", "@generate:open-api-doc"], "check:composer": "bin/echo-title 'composer normalize' && composer normalize --dry-run --no-update-lock", "check:composer:fix": "bin/echo-title 'composer normalize fix' && composer normalize --no-update-lock", "check:cs": "bin/echo-title 'phpcs' && php -d xdebug.mode=off vendor/bin/phpcs", "check:cs:files": "bin/echo-title 'phpcs files' && php -d xdebug.mode=off vendor/bin/phpcs", "check:cs:fix": "bin/echo-title 'phpcs fix' && php -d xdebug.mode=off vendor/bin/phpcbf", "check:json": "bin/echo-title 'Check json files' && php -d xdebug.mode=off bin/check-json-files.php", "check:lint": " bin/echo-title 'Lint' && parallel-lint --gitlab --show-deprecated -e php -j $(nproc) bin config docker docs migrations public src templates tests tools translations", "check:lint:files": "bin/echo-title 'Lint' && parallel-lint --show-deprecated -e php -j $(nproc)", "check:rector": "bin/echo-title 'rector' && php -d xdebug.mode=off ./vendor/bin/rector process --dry-run", "check:rector:fix": "bin/echo-title 'rector fix' && php -d xdebug.mode=off ./vendor/bin/rector process", "check:schema": ["php bin/echo-title 'Check schema after migrations'", "@clear:doctrine-cache", "php bin/console doctrine:schema:validate", "php bin/console doctrine:migrations:migrate -vvv"], "check:tests": ["Composer\\Config::disableProcessTimeout", "bin/echo-title 'phpunit tests' && php -d memory_limit=2G -d xdebug.mode=off vendor/bin/phpunit tests --colors=always --stderr --stop-on-failure"], "check:tests:all": "bin/echo-title 'phpunit tests' && php -d memory_limit=2G -d xdebug.mode=off vendor/bin/phpunit tests --colors=always --stderr", "check:tests:services-definition": ["php bin/echo-title 'Services definition test'", "phpunit --colors=always ./tests/ServicesTest.php"], "check:types": "bin/echo-title 'phpstan' && php -d memory_limit=2G -d xdebug.mode=off ./vendor/bin/phpstan analyse -c phpstan.neon", "check:ws": "bin/echo-title 'editor config' && vendor/bin/ec src tests config migrations docs bin baseline.neon composer.json phpstan.neon README.md docker-compose.yml docker-compose.override.yaml.dist", "check:ws:files": "php bin/echo-title 'Check Editor config files formatting' && vendor/bin/ec", "clear:doctrine-cache": ["php -d xdebug.mode=off bin/console doctrine:cache:clear-metadata", "php -d xdebug.mode=off bin/console doctrine:cache:clear-query", "php -d xdebug.mode=off bin/console doctrine:cache:clear-result"], "fix": ["@check:cs:fix", "@check:composer:fix", "@check:rector:fix"], "generate:baseline:phpstan": "php -d xdebug.mode=off vendor/bin/phpstan analyse --generate-baseline=baseline.neon -vvv && sed -i -e 's/\\t/    /g' baseline.neon", "generate:migration": ["php bin/echo-title 'Generate migration diff'", "@clear:doctrine-cache", "bin/console doctrine:database:drop --force", "bin/console doctrine:database:create", "bin/console doctrine:migrations:migrate --allow-no-migration --no-interaction -vvv", "bin/console doctrine:schema:update --dump-sql", "bin/console doctrine:migrations:diff --no-interaction -vvv"], "generate:open-api-doc": "bin/console nelmio:apidoc:dump --format json --server-url https://pm.dev.m2c.eu > docs/openApi/backend.json", "prepare-database": ["bin/console doctrine:cache:clear-metadata", "bin/console doctrine:database:drop --force --if-exists", "bin/console doctrine:database:create", "bin/console doctrine:schema:create --no-interaction", "bin/console doctrine:migrations:sync-metadata-storage --no-interaction", "bin/console doctrine:migrations:version --no-interaction --add --all", "bin/console doctrine:query:sql \"CREATE EXTENSION IF NOT EXISTS unaccent\"", "bin/console doctrine:query:sql \"CREATE EXTENSION IF NOT EXISTS pg_trgm\""], "prepare-test-database": ["bin/console doctrine:cache:clear-metadata --env=test", "bin/console doctrine:database:drop --force --if-exists --env=test", "bin/console doctrine:database:create --env=test", "bin/console doctrine:schema:create --env=test --no-interaction", "bin/console doctrine:migrations:sync-metadata-storage --env=test --no-interaction", "bin/console doctrine:migrations:version --env=test --no-interaction --add --all", "bin/console doctrine:query:sql --env=test \"CREATE EXTENSION IF NOT EXISTS unaccent\"", "bin/console doctrine:query:sql --env=test \"CREATE EXTENSION IF NOT EXISTS pg_trgm\""]}}